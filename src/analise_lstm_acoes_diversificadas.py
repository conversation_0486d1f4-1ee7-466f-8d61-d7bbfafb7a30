#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando LSTM
Baseado no exemplo do Medium e adaptado para usar dados OHLC + Volume + Spread
Usa os mesmos dados do script Ka<PERSON> para comparação
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
import sys
from datetime import datetime, timedelta
import math
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam

# Adicionar o diretório src ao path para importar functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

# Configurar TensorFlow para usar menos memória
tf.config.experimental.enable_memory_growth = True

LOOKBACK = 5

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)

        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def preparar_dados_lstm(dados, features=['Media_OHLC'], lookback=20):
    """
    Prepara os dados para o modelo LSTM para prever todas as features

    Args:
        dados: DataFrame com dados históricos
        features: Lista de features a serem usadas
        lookback: Número de dias anteriores para usar como entrada

    Returns:
        X_train, y_train, X_test, y_test, scaler, indices
    """
    # Selecionar features e remover NaN
    data = dados[features].dropna()

    if len(data) < lookback + 100:  # Mínimo de dados necessários para 15 anos
        raise ValueError(f"Dados insuficientes: {len(data)} dias, mínimo {lookback + 100}")

    # Normalizar todas as features com um único scaler
    scaler = MinMaxScaler(feature_range=(0, 1))
    features_scaled = scaler.fit_transform(data[features])

    # Preparar sequências: usar todas as features dos últimos 'lookback' dias
    # para prever todas as features do próximo dia
    X, y = [], []

    for i in range(lookback, len(features_scaled)):
        # X: sequência de 'lookback' dias com todas as features
        # Usar dados de i-lookback até i-1 (não inclui o dia atual)
        X.append(features_scaled[i-lookback:i])

        # y: todas as features do dia atual (dia i)
        y.append(features_scaled[i])

    X = np.array(X)
    y = np.array(y)

    # Dividir em treino e teste (75% treino, 25% teste)
    train_size = int(len(X) * 0.75)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    return X_train, y_train, X_test, y_test, scaler, data.index[lookback:]

def criar_modelo_lstm(input_shape, num_features, lstm_units=[50, 50], dropout_rate=0.2):
    """
    Cria modelo LSTM para previsão de todas as features

    Args:
        input_shape: Formato dos dados de entrada (timesteps, features)
        num_features: Número de features a serem previstas
        lstm_units: Lista com número de unidades LSTM em cada camada
        dropout_rate: Taxa de dropout para regularização

    Returns:
        Modelo LSTM compilado
    """
    model = Sequential()

    # Primeira camada LSTM
    model.add(LSTM(lstm_units[0], return_sequences=True, input_shape=input_shape))
    model.add(Dropout(dropout_rate))

    # Camadas LSTM adicionais
    for i in range(1, len(lstm_units)):
        return_seq = i < len(lstm_units) - 1
        model.add(LSTM(lstm_units[i], return_sequences=return_seq))
        model.add(Dropout(dropout_rate))

    # Camada de saída para prever todas as features
    model.add(Dense(num_features))

    # Compilar modelo
    model.compile(optimizer=Adam(learning_rate=0.001),
                  loss='mean_squared_error',
                  metrics=['mae'])

    return model

def treinar_modelo_lstm(model, X_train, y_train, X_test, y_test, epochs=50, batch_size=32):
    """
    Treina o modelo LSTM
    """
    # Callback para parar treinamento se não houver melhoria
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss', patience=15, restore_best_weights=True  # Mais paciência para 5 anos de dados
    )

    # Treinar modelo
    history = model.fit(
        X_train, y_train,
        epochs=epochs,
        batch_size=batch_size,
        validation_data=(X_test, y_test),
        callbacks=[early_stopping],
        verbose=0
    )

    return history

def fazer_previsoes_lstm(model, dados, features, scaler, lookback=20, dias_previsao=20):
    """
    Faz previsões futuras usando o modelo LSTM para todas as features

    IMPORTANTE: Esta função faz previsões "livres" onde usa suas próprias previsões
    como entrada para os próximos dias. Diferente do teste que usa dados reais.

    Args:
        model: Modelo LSTM treinado
        dados: DataFrame com dados históricos
        features: Lista de features usadas
        scaler: Scaler das features
        lookback: Janela de lookback
        dias_previsao: Número de dias para prever

    Returns:
        Array com previsões da média OHLC (para compatibilidade com gráficos)
    """
    # Preparar dados
    data = dados[features].dropna()

    if len(data) < lookback:
        raise ValueError(f"Dados insuficientes para previsão: {len(data)} < {lookback}")

    # Normalizar dados
    features_scaled = scaler.transform(data)

    # Usar os últimos 20 dias REAIS como entrada inicial
    current_sequence = features_scaled[-lookback:].reshape(1, lookback, -1)

    previsoes_media_ohlc = []  # Média OHLC para os gráficos

    for _ in range(dias_previsao):
        # Prever próximo valor (todas as features)
        next_pred_scaled = model.predict(current_sequence, verbose=0)
        # Desnormalizar todas as previsões
        next_pred_all = scaler.inverse_transform(next_pred_scaled)[0]

        # Guardar a média OHLC para retorno (primeira feature)
        previsoes_media_ohlc.append(next_pred_all[0])

        # AQUI É A DIFERENÇA: Para previsões futuras, usar as próprias previsões
        # como entrada para o próximo dia (previsão "livre")
        new_point = next_pred_scaled[0]

        # Atualizar sequência (remover primeiro dia, adicionar previsão no final)
        current_sequence = np.roll(current_sequence, -1, axis=1)
        current_sequence[0, -1, :] = new_point

    return np.array(previsoes_media_ohlc)

def obter_dados_com_lstm(ticker, nome, data_corte=None):
    """
    Obtém dados e aplica modelo LSTM

    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
        data_corte: Data limite para usar nos dados (formato 'YYYY-MM-DD' ou datetime)
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)
        # Obter 15 anos para ter muito mais dados e melhorar o modelo
        dados = stock.history(period="15y")

        if dados.empty or len(dados) < 1000:  # Mínimo de ~4 anos para aproveitar os 15 anos
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        # Guardar dados completos para exibição no gráfico
        dados_completos = dados.copy()

        # Aplicar corte de data se especificado (apenas para treinamento do LSTM)
        if data_corte is not None:
            if isinstance(data_corte, str):
                data_corte = pd.to_datetime(data_corte)

            # Garantir que a data de corte tenha o mesmo timezone dos dados
            if dados.index.tz is not None:
                if hasattr(data_corte, 'tz') and data_corte.tz is None:
                    data_corte = data_corte.tz_localize(dados.index.tz)
                elif not hasattr(data_corte, 'tz'):
                    data_corte = pd.Timestamp(data_corte).tz_localize(dados.index.tz)

            # Filtrar dados até a data de corte APENAS para treinamento
            dados_treino = dados[dados.index <= data_corte]

            if dados_treino.empty or len(dados_treino) < 500:  # Mínimo após corte para 15 anos
                print(f"     ⚠️ Dados insuficientes após corte de data ({len(dados_treino)} dias)")
                return None

            print(f"     📅 Treinamento até {data_corte.strftime('%Y-%m-%d')} ({len(dados_treino)} dias)")
            print(f"     📊 Exibindo dados completos até {dados_completos.index[-1].strftime('%Y-%m-%d')} ({len(dados_completos)} dias)")

            # Usar dados de treino para o LSTM
            dados = dados_treino
        else:
            print(f"     📅 Usando todos os dados disponíveis ({len(dados)} dias)")
            data_corte = None

        # Calcular média OHLC
        dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

        # Calcular spread usando edge_rolling
        try:
            spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=20)
            dados['Spread'] = spread_estimado * 100  # Converter para percentual
        except Exception as e:
            print(f"     ⚠️ Erro ao calcular spread: {e}")
            # Fallback: usar volatilidade como proxy para spread
            returns = dados['Media_OHLC'].pct_change().dropna()
            volatilidade = returns.rolling(window=20).std()
            dados['Spread'] = volatilidade * 100

        # Preencher NaN no spread
        dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())

        # Calcular médias móveis baseadas na média OHLC
        dados['MM25'] = dados['Media_OHLC'].rolling(window=25).mean()
        dados['MM200'] = dados['Media_OHLC'].rolling(window=200).mean()

        # Calcular tendência como diferença entre médias OHLC consecutivas
        dados['Tendencia'] = dados['Media_OHLC'].diff()

        # Preparar features para LSTM (Média OHLC + Volume + Spread)
        features = ['Media_OHLC', 'Volume', 'Spread']

        # Preparar dados para LSTM
        X_train, y_train, X_test, y_test, scaler, _ = preparar_dados_lstm(
            dados, features=features, lookback=LOOKBACK
        )

        print(f"     🧠 Dados LSTM: {len(X_train)} treino, {len(X_test)} teste")
        print(f"     🔧 Features usadas: {features}")
        print(f"     🎯 Prevendo todas as features: {len(features)} saídas")

        # Criar e treinar modelo LSTM
        model = criar_modelo_lstm(
            input_shape=(X_train.shape[1], X_train.shape[2]),
            num_features=len(features),
            lstm_units=[16, 8],
            dropout_rate=0.2
        )

        print(f"     🔄 Treinando modelo LSTM...")
        history = treinar_modelo_lstm(
            model, X_train, y_train, X_test, y_test,
            epochs=100, batch_size=32  # Mais épocas para aproveitar os 5 anos de dados
        )

        # Fazer previsões no conjunto de teste
        # X_test já contém as sequências corretas (dados reais dos 60 dias anteriores)
        y_pred_scaled = model.predict(X_test, verbose=0)
        print(y_pred_scaled.shape)

        # Desnormalizar previsões para cálculo de métricas (apenas Média OHLC)
        y_pred_all = scaler.inverse_transform(y_pred_scaled)
        y_test_all = scaler.inverse_transform(y_test)

        # Extrair apenas Média OHLC (primeira coluna) para métricas
        y_pred_media_ohlc = y_pred_all[:, 0]  # Média OHLC é a primeira feature
        y_test_media_ohlc = y_test_all[:, 0]

        # Calcular métricas apenas para Média OHLC
        rmse = math.sqrt(mean_squared_error(y_test_media_ohlc, y_pred_media_ohlc))
        mae = np.mean(np.abs(y_test_media_ohlc - y_pred_media_ohlc))

        print(f"     📈 RMSE (Média OHLC): {rmse:.2f}, MAE (Média OHLC): {mae:.2f}")

        # Adicionar previsões LSTM aos dados (apenas Média OHLC para visualização)
        lstm_predictions = np.full(len(dados), np.nan)

        # Calcular índice de início das previsões no dataset original
        start_idx = LOOKBACK + len(X_train)  # lookback + dados de treino
        end_idx = start_idx + len(y_pred_media_ohlc)

        if end_idx <= len(dados):
            lstm_predictions[start_idx:end_idx] = y_pred_media_ohlc

        dados['LSTM'] = lstm_predictions
        print(len(dados))
        # Fazer previsões futuras (retorna apenas Média OHLC)
        previsoes_futuras = fazer_previsoes_lstm(
            model, dados, features, scaler,
            lookback=LOOKBACK, dias_previsao=20
        )

        # Preparar dados completos para exibição
        if data_corte is not None:
            # Calcular média OHLC para dados completos
            dados_completos['Media_OHLC'] = (dados_completos['Open'] + dados_completos['Close'] +
                                           dados_completos['Low'] + dados_completos['High']) / 4

            # Calcular features para dados completos
            try:
                spread_completo = edge_rolling(dados_completos[['Open', 'High', 'Low', 'Close']], window=20)
                dados_completos['Spread'] = spread_completo * 100
            except Exception:
                returns_completo = dados_completos['Media_OHLC'].pct_change().dropna()
                volatilidade_completo = returns_completo.rolling(window=20).std()
                dados_completos['Spread'] = volatilidade_completo * 100

            dados_completos['Spread'] = dados_completos['Spread'].fillna(dados_completos['Spread'].mean())
            dados_completos['MM25'] = dados_completos['Media_OHLC'].rolling(window=25).mean()
            dados_completos['MM200'] = dados_completos['Media_OHLC'].rolling(window=200).mean()
            dados_completos['Tendencia'] = dados_completos['Media_OHLC'].diff()

            # Para o LSTM nos dados completos, usar apenas até a data de corte para treino
            lstm_completo = np.full(len(dados_completos), np.nan)
            lstm_completo[:len(dados)] = lstm_predictions[:len(dados)]
            dados_completos['LSTM'] = lstm_completo

            # Pegar últimos 12 meses dos dados completos
            dados_12m = dados_completos.tail(252)
        else:
            # Sem corte, usar dados normalmente
            dados_12m = dados.tail(252)

        # Informar sobre previsões
        ultima_data_treino = dados.index[-1]
        if data_corte:
            print(f"     ✅ {len(dados_12m)} dias exibidos (com dados reais completos)")
            print(f"     📅 Previsões a partir de: {ultima_data_treino.strftime('%Y-%m-%d')}")
        else:
            print(f"     ✅ {len(dados_12m)} dias (com LSTM e MM200)")

        return dados_12m, previsoes_futuras, model, data_corte, rmse, mae, history

    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia_lstm(dados):
    """
    Analisa a tendência baseada no modelo LSTM e MM200
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Media_OHLC'].iloc[-1]
    lstm_atual = dados['LSTM'].iloc[-1] if not pd.isna(dados['LSTM'].iloc[-1]) else preco_atual
    mm200_atual = dados['MM200'].iloc[-1]

    # Verificar se os valores estão válidos
    if pd.isna(mm200_atual):
        return "Indefinida", "gray"

    # Análise de tendência baseada em LSTM
    if preco_atual > lstm_atual > mm200_atual:
        return "Altista Forte", "darkgreen"
    elif preco_atual > lstm_atual and lstm_atual < mm200_atual:
        return "Altista Moderada", "green"
    elif preco_atual < lstm_atual < mm200_atual:
        return "Baixista Forte", "darkred"
    elif preco_atual < lstm_atual and lstm_atual > mm200_atual:
        return "Baixista Moderada", "red"
    else:
        return "Lateral", "orange"

def criar_grafico_lstm(ticker, nome, dados, previsoes, data_corte=None, rmse=None, mae=None, history=None):
    """
    Cria gráfico com média OHLC, LSTM, MM200, previsões e tendência
    """
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 16))

    # Calcular estatísticas
    preco_inicial = dados['Media_OHLC'].iloc[0]
    preco_final = dados['Media_OHLC'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100

    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_lstm(dados)

    # Gráfico 1: Média OHLC + LSTM + MM200 + Previsões
    ax1.plot(dados.index, dados['Media_OHLC'], linewidth=2.5, color='#1f77b4',
             label='Média OHLC', zorder=3)

    # LSTM (substitui Kalman)
    if 'LSTM' in dados.columns and not dados['LSTM'].isna().all():
        ax1.plot(dados.index, dados['LSTM'], linewidth=2, color='orange',
                 label='Previsão LSTM', alpha=0.8, zorder=2)

        # Área entre média OHLC e LSTM
        ax1.fill_between(dados.index, dados['Media_OHLC'], dados['LSTM'],
                         where=(dados['Media_OHLC'] >= dados['LSTM']),
                         color='lightgreen', alpha=0.3, interpolate=True,
                         label='Média OHLC > LSTM')
        ax1.fill_between(dados.index, dados['Media_OHLC'], dados['LSTM'],
                         where=(dados['Media_OHLC'] < dados['LSTM']),
                         color='lightcoral', alpha=0.3, interpolate=True,
                         label='Média OHLC < LSTM')

    # Médias móveis
    ax1.plot(dados.index, dados['MM25'], linewidth=2, color='green',
             label='MM 25 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red',
             label='MM 200 dias', alpha=0.8, zorder=2)

    # Marcar data de corte se especificada
    if data_corte is not None:
        ax1.axvline(x=data_corte, color='red', linestyle='--', alpha=0.7, linewidth=2,
                   label='Data de Corte (Limite do Treinamento)', zorder=4)

    # Adicionar previsões se disponíveis
    if previsoes is not None and len(previsoes) > 0:
        # Determinar data de início das previsões
        if data_corte is not None:
            data_inicio_previsao = data_corte
        else:
            data_inicio_previsao = dados.index[-1]

        # Criar datas futuras para as previsões
        datas_futuras = pd.date_range(start=data_inicio_previsao + timedelta(days=1),
                                     periods=len(previsoes), freq='D')

        # Plotar previsões
        ax1.plot(datas_futuras, previsoes, linewidth=2, color='purple',
                linestyle='--', label='Previsão LSTM (20 dias)', alpha=0.8, zorder=2)

        # Conectar ponto de início com primeira previsão
        if data_corte is not None:
            dados_ate_corte = dados[dados.index <= data_corte]
            if not dados_ate_corte.empty:
                preco_corte = dados_ate_corte['Media_OHLC'].iloc[-1]
                ax1.plot([data_inicio_previsao, datas_futuras[0]],
                        [preco_corte, previsoes[0]],
                        linewidth=1, color='purple', linestyle=':', alpha=0.6)
        else:
            ax1.plot([data_inicio_previsao, datas_futuras[0]],
                    [dados['Media_OHLC'].iloc[-1], previsoes[0]],
                    linewidth=1, color='purple', linestyle=':', alpha=0.6)

    # Título do gráfico
    titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Análise LSTM com Média OHLC + MM25 + MM200 (12 Meses)'
    if data_corte is not None:
        titulo_base += f'\n🔴 Treinamento até {data_corte.strftime("%Y-%m-%d")} | Dados reais completos exibidos'

    ax1.set_title(titulo_base, fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Estatísticas no gráfico
    lstm_atual = dados['LSTM'].iloc[-1] if 'LSTM' in dados.columns and not pd.isna(dados['LSTM'].iloc[-1]) else 0
    mm25_atual = dados['MM25'].iloc[-1] if not pd.isna(dados['MM25'].iloc[-1]) else 0
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Média OHLC: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'LSTM: R$ {lstm_atual:.2f} | MM25: R$ {mm25_atual:.2f} | MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'

    if rmse is not None and mae is not None:
        stats_text += f'\nRMSE: {rmse:.2f} | MAE: {mae:.2f}'

    if previsoes is not None and len(previsoes) > 0:
        previsao_final = previsoes[-1]
        variacao_previsao = ((previsao_final / preco_final) - 1) * 100
        stats_text += f'\nPrevisão 20d: R$ {previsao_final:.2f} ({variacao_previsao:+.1f}%)'

    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9,
                      edgecolor=cor_tendencia, linewidth=2))

    # Gráfico 2: Spread (entrada do modelo)
    if 'Spread' in dados.columns:
        ax2.plot(dados.index, dados['Spread'], alpha=0.7, color='purple', linewidth=1.5)
        ax2.set_title('Spread Bid/Ask (%) - Feature do Modelo LSTM', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Spread (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)

    # Gráfico 3: Loss durante o treinamento
    if history is not None:
        epochs = range(1, len(history.history['loss']) + 1)

        ax3.plot(epochs, history.history['loss'], 'b-', linewidth=2, label='Loss Treino', alpha=0.8)
        if 'val_loss' in history.history:
            ax3.plot(epochs, history.history['val_loss'], 'r-', linewidth=2, label='Loss Validação', alpha=0.8)

        ax3.set_xlabel('Épocas', fontsize=12)
        ax3.set_ylabel('Loss (MSE)', fontsize=12)
        ax3.set_title('Evolução da Loss Durante o Treinamento LSTM', fontsize=14, fontweight='bold')
        ax3.legend(loc='upper right', fontsize=10)
        ax3.grid(True, alpha=0.3)

        # Adicionar informações sobre o treinamento
        final_train_loss = history.history['loss'][-1]
        final_val_loss = history.history['val_loss'][-1] if 'val_loss' in history.history else None

        info_text = f'Loss Final Treino: {final_train_loss:.6f}'
        if final_val_loss:
            info_text += f'\nLoss Final Validação: {final_val_loss:.6f}'
            info_text += f'\nÉpocas Treinadas: {len(epochs)}'

        ax3.text(0.02, 0.98, info_text, transform=ax3.transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))
    else:
        ax3.text(0.5, 0.5, 'Histórico de treinamento não disponível', transform=ax3.transAxes,
                ha='center', va='center', fontsize=14, color='gray')
        ax3.set_title('Loss do Treinamento LSTM', fontsize=14, fontweight='bold')

    ax3.set_xlabel('Data', fontsize=12)
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/lstm_analysis', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"results/figures/lstm_analysis/lstm_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")

    resultado = {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,  # Agora é a média OHLC
        'lstm': lstm_atual,
        'mm25': mm25_atual,
        'mm200': mm200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia,
        'rmse': rmse,
        'mae': mae
    }

    if previsoes is not None and len(previsoes) > 0:
        resultado['previsao_20d'] = previsoes[-1]
        resultado['variacao_previsao'] = ((previsoes[-1] / preco_final) - 1) * 100

    return resultado

def gerar_relatorio_lstm(resultados):
    """
    Gera relatório detalhado da análise com LSTM
    """
    print("\n" + "="*120)
    print("🧠 RELATÓRIO ANÁLISE TÉCNICA - LSTM + SPREAD + MM200")
    print("="*120)

    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Média OHLC':<12} {'LSTM':<8} {'MM25':<8} {'MM200':<8} {'RMSE':<8} {'MAE':<8} {'Prev20d':<8} {'Tendência':<15}")
    print("-" * 140)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"
        prev_text = f"{r.get('previsao_20d', 0):.2f}" if 'previsao_20d' in r else "N/A"
        rmse_text = f"{r.get('rmse', 0):.2f}" if r.get('rmse') else "N/A"
        mae_text = f"{r.get('mae', 0):.2f}" if r.get('mae') else "N/A"

        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>10.2f} {r['lstm']:>6.2f} {r['mm25']:>6.2f} {r['mm200']:>6.2f} "
              f"{rmse_text:>6s} {mae_text:>6s} {prev_text:>6s} {r['tendencia']:<15}")

    # Estatísticas por tendência
    print("\n" + "="*140)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*140)

    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])

    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")

    # Análise de posicionamento
    print("\n" + "="*140)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*140)

    acima_lstm = len([r for r in resultados if r['preco_atual'] > r['lstm']])
    acima_mm25 = len([r for r in resultados if r['preco_atual'] > r['mm25']])
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])
    lstm_acima_mm200 = len([r for r in resultados if r['lstm'] > r['mm200']])
    mm25_acima_mm200 = len([r for r in resultados if r['mm25'] > r['mm200']])

    print(f"Ações com Média OHLC acima do LSTM:   {acima_lstm:2d}/{len(resultados)} ({acima_lstm/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM25:   {acima_mm25:2d}/{len(resultados)} ({acima_mm25/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM200:  {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")
    print(f"Ações com LSTM > MM200:              {lstm_acima_mm200:2d}/{len(resultados)} ({lstm_acima_mm200/len(resultados)*100:.1f}%)")
    print(f"Ações com MM25 > MM200:              {mm25_acima_mm200:2d}/{len(resultados)} ({mm25_acima_mm200/len(resultados)*100:.1f}%)")

    # Análise de métricas LSTM
    rmses = [r['rmse'] for r in resultados if r.get('rmse')]
    maes = [r['mae'] for r in resultados if r.get('mae')]

    if rmses and maes:
        print(f"\n🧠 MÉTRICAS DO MODELO LSTM")
        print("="*140)
        print(f"RMSE médio: {np.mean(rmses):.2f} | RMSE mediano: {np.median(rmses):.2f}")
        print(f"MAE médio:  {np.mean(maes):.2f} | MAE mediano:  {np.median(maes):.2f}")

    # Análise de previsões
    previsoes_positivas = len([r for r in resultados if r.get('variacao_previsao', 0) > 0])
    if previsoes_positivas > 0:
        print(f"\n🔮 ANÁLISE DE PREVISÕES (20 DIAS)")
        print("="*140)
        print(f"Previsões positivas: {previsoes_positivas:2d}/{len(resultados)} ({previsoes_positivas/len(resultados)*100:.1f}%)")

        # Top 5 previsões mais otimistas
        with_predictions = [r for r in resultados if 'variacao_previsao' in r]
        top_predictions = sorted(with_predictions, key=lambda x: x['variacao_previsao'], reverse=True)[:5]

        print("\nTOP 5 Previsões Mais Otimistas:")
        for i, r in enumerate(top_predictions, 1):
            print(f"{i}. {r['ticker'].replace('.SA', ''):<6} - {r['nome'][:20]:<20} | "
                  f"Previsão: {r['variacao_previsao']:+6.1f}%")

def main():
    print("🧠 ANÁLISE COM LSTM - AÇÕES DIVERSIFICADAS")
    print("="*80)
    print("📊 Análise Técnica com:")
    print("   • Média OHLC (Open + Close + Low + High) / 4")
    print("   • Modelo LSTM com features Média OHLC + Volume + Spread")
    print("   • Média Móvel de 25 dias (MM25) baseada na Média OHLC")
    print("   • Média Móvel de 200 dias (MM200) baseada na Média OHLC")
    print("   • Previsão 20 dias à frente")
    print("   • Análise de tendência")
    print("   • Métricas de performance (RMSE, MAE)")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")

    # Perguntar sobre data de corte para teste
    print("\n📅 CONFIGURAÇÃO DE DATA DE CORTE (para testes)")
    print("="*50)
    print("Você pode escolher uma data limite para o modelo LSTM.")
    print("O modelo usará apenas dados até essa data para fazer previsões.")
    print("Isso é útil para testar a precisão das previsões.")
    print()
    print("Opções:")
    print("1. Usar todos os dados disponíveis (padrão)")
    print("2. Especificar uma data de corte")

    opcao_data = input("\nEscolha uma opção (1/2): ").strip()
    data_corte = None

    if opcao_data == '2':
        while True:
            try:
                data_input = input("Digite a data de corte (YYYY-MM-DD): ").strip()
                data_corte = pd.to_datetime(data_input)
                print(f"✅ Data de corte definida: {data_corte.strftime('%Y-%m-%d')}")
                print(f"   O modelo usará apenas dados até esta data para previsões.")
                break
            except:
                print("❌ Formato de data inválido. Use YYYY-MM-DD (ex: 2024-01-15)")
    else:
        print("✅ Usando todos os dados disponíveis (sem corte)")

    print("⏱️  Tempo estimado: 20-30 minutos (treinamento LSTM com 15 anos de dados)")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    if data_corte:
        print(f"\n🧠 Iniciando análise com LSTM (dados até {data_corte.strftime('%Y-%m-%d')})...")
    else:
        print(f"\n🧠 Iniciando análise com LSTM...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        resultado_dados = obter_dados_com_lstm(ticker, nome, data_corte)

        if resultado_dados is not None:
            if len(resultado_dados) == 7:
                dados, previsoes, _, data_corte_usada, rmse, mae, history = resultado_dados
                resultado = criar_grafico_lstm(ticker, nome, dados, previsoes, data_corte_usada, rmse, mae, history)
                resultados.append(resultado)
                sucesso += 1
            else:
                erro += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if data_corte:
        print(f"   📅 Data de corte utilizada: {data_corte.strftime('%Y-%m-%d')}")
        print(f"   🔮 Previsões feitas a partir desta data")
    else:
        print(f"   📅 Dados completos utilizados (sem corte)")

    if resultados:
        # Gerar relatório
        gerar_relatorio_lstm(resultados)

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame(resultados)
            os.makedirs('results/csv/lstm_analysis', exist_ok=True)
            csv_path = 'results/csv/lstm_analysis/resultados_lstm.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados salvos em: {csv_path}")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
