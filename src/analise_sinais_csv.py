#!/usr/bin/env python3
"""
Script para análise dos sinais de compra e venda salvos em CSV
Gera estatísticas e visualizações dos sinais gerados pelo XGBoost
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def carregar_dados_sinais():
    """
    Carrega os dados de sinais do arquivo CSV
    """
    csv_path = 'results/sinais_xgboost_completo.csv'
    
    if not os.path.exists(csv_path):
        print(f"❌ Arquivo não encontrado: {csv_path}")
        print("Execute primeiro o script classificador_xgboost_sinais.py")
        return None
    
    df = pd.read_csv(csv_path)
    df['Data'] = pd.to_datetime(df['Data'])
    
    print(f"✅ Dados carregados: {len(df):,} registros")
    print(f"📅 Período: {df['Data'].min().strftime('%Y-%m-%d')} a {df['Data'].max().strftime('%Y-%m-%d')}")
    print(f"📈 Ações: {df['Ticker'].nunique()}")
    
    return df

def analisar_estatisticas_gerais(df):
    """
    Análise estatística geral dos sinais
    """
    print("\n📊 ESTATÍSTICAS GERAIS DOS SINAIS")
    print("=" * 50)
    
    total_registros = len(df)
    total_compra = df['Sinal_Compra'].sum()
    total_venda = df['Sinal_Venda'].sum()
    
    print(f"Total de registros: {total_registros:,}")
    print(f"Sinais de Compra: {total_compra:,} ({total_compra/total_registros*100:.1f}%)")
    print(f"Sinais de Venda: {total_venda:,} ({total_venda/total_registros*100:.1f}%)")
    print(f"Sem sinal: {total_registros - total_compra - total_venda:,}")
    
    # Estatísticas por ano
    df['Ano'] = df['Data'].dt.year
    stats_ano = df.groupby('Ano').agg({
        'Sinal_Compra': 'sum',
        'Sinal_Venda': 'sum',
        'Ticker': 'count'
    }).rename(columns={'Ticker': 'Total_Registros'})
    
    print(f"\n📅 Estatísticas por Ano:")
    for ano, row in stats_ano.iterrows():
        total = row['Total_Registros']
        compra = row['Sinal_Compra']
        venda = row['Sinal_Venda']
        print(f"  {ano}: {compra:,} compras ({compra/total*100:.1f}%), {venda:,} vendas ({venda/total*100:.1f}%)")

def analisar_por_acao(df):
    """
    Análise dos sinais por ação
    """
    print(f"\n📈 ANÁLISE POR AÇÃO")
    print("=" * 50)
    
    # Resumo por ação
    resumo = df.groupby('Ticker').agg({
        'Sinal_Compra': 'sum',
        'Sinal_Venda': 'sum',
        'Media_OHLC': ['first', 'last', 'mean'],
        'Volume': 'mean',
        'Volatilidade': 'mean'
    }).round(2)
    
    # Flatten column names
    resumo.columns = ['Sinais_Compra', 'Sinais_Venda', 'Preco_Inicial', 'Preco_Final', 'Preco_Medio', 'Volume_Medio', 'Volatilidade_Media']
    resumo['Performance_%'] = ((resumo['Preco_Final'] / resumo['Preco_Inicial']) - 1) * 100
    resumo['Total_Sinais'] = resumo['Sinais_Compra'] + resumo['Sinais_Venda']
    
    # Top 5 ações com mais sinais de compra
    print(f"🟢 Top 5 - Mais Sinais de Compra:")
    top_compra = resumo.nlargest(5, 'Sinais_Compra')
    for ticker, row in top_compra.iterrows():
        print(f"  {ticker}: {row['Sinais_Compra']} sinais ({row['Performance_%']:+.1f}% performance)")
    
    # Top 5 ações com mais sinais de venda
    print(f"\n🔴 Top 5 - Mais Sinais de Venda:")
    top_venda = resumo.nlargest(5, 'Sinais_Venda')
    for ticker, row in top_venda.iterrows():
        print(f"  {ticker}: {row['Sinais_Venda']} sinais ({row['Performance_%']:+.1f}% performance)")
    
    # Melhores performances
    print(f"\n🚀 Top 5 - Melhor Performance:")
    top_performance = resumo.nlargest(5, 'Performance_%')
    for ticker, row in top_performance.iterrows():
        print(f"  {ticker}: {row['Performance_%']:+.1f}% (Compra: {row['Sinais_Compra']}, Venda: {row['Sinais_Venda']})")
    
    return resumo

def criar_visualizacoes(df, resumo):
    """
    Cria visualizações dos sinais
    """
    print(f"\n📊 Criando visualizações...")
    
    # Configurar estilo
    plt.style.use('default')
    
    # Criar figura com múltiplos subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Análise dos Sinais XGBoost - 5 Anos de Dados', fontsize=16, fontweight='bold')
    
    # 1. Distribuição de sinais por ano
    df['Ano'] = df['Data'].dt.year
    sinais_ano = df.groupby('Ano').agg({
        'Sinal_Compra': 'sum',
        'Sinal_Venda': 'sum'
    })
    
    sinais_ano.plot(kind='bar', ax=axes[0,0], color=['green', 'red'], alpha=0.7)
    axes[0,0].set_title('Sinais por Ano')
    axes[0,0].set_xlabel('Ano')
    axes[0,0].set_ylabel('Quantidade de Sinais')
    axes[0,0].legend(['Compra', 'Venda'])
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Top 10 ações com mais sinais
    top_sinais = resumo.nlargest(10, 'Total_Sinais')[['Sinais_Compra', 'Sinais_Venda']]
    top_sinais.plot(kind='barh', ax=axes[0,1], color=['green', 'red'], alpha=0.7)
    axes[0,1].set_title('Top 10 Ações - Total de Sinais')
    axes[0,1].set_xlabel('Quantidade de Sinais')
    axes[0,1].legend(['Compra', 'Venda'])
    
    # 3. Relação Performance vs Sinais
    axes[1,0].scatter(resumo['Total_Sinais'], resumo['Performance_%'], alpha=0.6, c='blue')
    axes[1,0].set_xlabel('Total de Sinais')
    axes[1,0].set_ylabel('Performance (%)')
    axes[1,0].set_title('Performance vs Total de Sinais')
    axes[1,0].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # Adicionar labels para ações extremas
    for ticker, row in resumo.iterrows():
        if abs(row['Performance_%']) > 100 or row['Total_Sinais'] > 1300:
            axes[1,0].annotate(ticker, (row['Total_Sinais'], row['Performance_%']), 
                             xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 4. Distribuição de sinais por mês
    df['Mes'] = df['Data'].dt.month
    sinais_mes = df.groupby('Mes').agg({
        'Sinal_Compra': 'sum',
        'Sinal_Venda': 'sum'
    })
    
    meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 
             'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
    
    x = np.arange(len(meses))
    width = 0.35
    
    axes[1,1].bar(x - width/2, sinais_mes['Sinal_Compra'], width, label='Compra', color='green', alpha=0.7)
    axes[1,1].bar(x + width/2, sinais_mes['Sinal_Venda'], width, label='Venda', color='red', alpha=0.7)
    axes[1,1].set_xlabel('Mês')
    axes[1,1].set_ylabel('Quantidade de Sinais')
    axes[1,1].set_title('Sazonalidade dos Sinais')
    axes[1,1].set_xticks(x)
    axes[1,1].set_xticklabels(meses)
    axes[1,1].legend()
    
    plt.tight_layout()
    
    # Salvar gráfico
    nome_arquivo = 'results/figures/analise_sinais_xgboost.png'
    os.makedirs(os.path.dirname(nome_arquivo), exist_ok=True)
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"   ✅ Gráfico salvo: {nome_arquivo}")

def analisar_eficacia_sinais(df):
    """
    Analisa a eficácia dos sinais comparando com o movimento real dos preços
    """
    print(f"\n🎯 ANÁLISE DE EFICÁCIA DOS SINAIS")
    print("=" * 50)
    
    # Calcular se o sinal foi correto
    df['Sinal_Compra_Correto'] = (df['Sinal_Compra'] == 1) & (df['Media_OHLC'] < df['Media_OHLC_Futura'])
    df['Sinal_Venda_Correto'] = (df['Sinal_Venda'] == 1) & (df['Media_OHLC'] > df['Media_OHLC_Futura'])
    
    # Estatísticas de acerto
    sinais_compra = df[df['Sinal_Compra'] == 1]
    sinais_venda = df[df['Sinal_Venda'] == 1]
    
    if len(sinais_compra) > 0:
        acerto_compra = sinais_compra['Sinal_Compra_Correto'].mean() * 100
        print(f"🟢 Eficácia Sinais de Compra: {acerto_compra:.1f}%")
    
    if len(sinais_venda) > 0:
        acerto_venda = sinais_venda['Sinal_Venda_Correto'].mean() * 100
        print(f"🔴 Eficácia Sinais de Venda: {acerto_venda:.1f}%")
    
    # Eficácia por ação
    eficacia_por_acao = df.groupby('Ticker').agg({
        'Sinal_Compra': 'sum',
        'Sinal_Compra_Correto': 'sum',
        'Sinal_Venda': 'sum',
        'Sinal_Venda_Correto': 'sum'
    })
    
    eficacia_por_acao['Eficacia_Compra_%'] = (eficacia_por_acao['Sinal_Compra_Correto'] / 
                                              eficacia_por_acao['Sinal_Compra'] * 100).fillna(0)
    eficacia_por_acao['Eficacia_Venda_%'] = (eficacia_por_acao['Sinal_Venda_Correto'] / 
                                             eficacia_por_acao['Sinal_Venda'] * 100).fillna(0)
    
    # Top 5 melhores eficácias
    print(f"\n🎯 Top 5 - Melhor Eficácia Compra:")
    top_eficacia_compra = eficacia_por_acao[eficacia_por_acao['Sinal_Compra'] >= 10].nlargest(5, 'Eficacia_Compra_%')
    for ticker, row in top_eficacia_compra.iterrows():
        print(f"  {ticker}: {row['Eficacia_Compra_%']:.1f}% ({row['Sinal_Compra_Correto']:.0f}/{row['Sinal_Compra']:.0f})")
    
    print(f"\n🎯 Top 5 - Melhor Eficácia Venda:")
    top_eficacia_venda = eficacia_por_acao[eficacia_por_acao['Sinal_Venda'] >= 10].nlargest(5, 'Eficacia_Venda_%')
    for ticker, row in top_eficacia_venda.iterrows():
        print(f"  {ticker}: {row['Eficacia_Venda_%']:.1f}% ({row['Sinal_Venda_Correto']:.0f}/{row['Sinal_Venda']:.0f})")

def main():
    """
    Função principal
    """
    print("📊 ANÁLISE DOS SINAIS XGBOOST")
    print("=" * 50)
    
    # Carregar dados
    df = carregar_dados_sinais()
    if df is None:
        return
    
    # Análises
    analisar_estatisticas_gerais(df)
    resumo = analisar_por_acao(df)
    analisar_eficacia_sinais(df)
    
    # Visualizações
    criar_visualizacoes(df, resumo)
    
    print(f"\n✅ Análise concluída!")
    print(f"📊 Verifique os gráficos em: results/figures/analise_sinais_xgboost.png")

if __name__ == "__main__":
    main()
