#!/usr/bin/env python3
"""
Script para classificação de sinais de compra e venda usando XGBoost
Baseado na média OHLC das ações diversificadas com sinais futuros
Usa features: média OHLC (10 dias passados), volume, spread e volatilidade
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import pickle
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths['acoes_diversificacao']
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def baixar_dados_acao(ticker, nome):
    """
    Baixa dados históricos de uma ação usando 5 anos de dados
    """
    try:
        # Usar 5 anos de dados para treino do XGBoost
        periodo = '5y'
        print(f"     📊 Baixando dados de {ticker} ({nome}) - período: {periodo}")

        # Baixar dados históricos
        dados = yf.download(ticker, period=periodo, progress=False)

        if dados.empty:
            print(f"     ❌ Nenhum dado encontrado para {ticker}")
            return None

        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)

        # Verificar se tem dados suficientes
        if len(dados) < 100:
            print(f"     ⚠️ Poucos dados para {ticker}: {len(dados)} dias")
            return None

        return dados

    except Exception as e:
        print(f"     ❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo penúltimo dia
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            # Obter o último valor como escalar
            ultimo_valor = dados[coluna].iloc[-1]
            if hasattr(ultimo_valor, 'item'):
                ultimo_valor = ultimo_valor.item()

            # Verificar se é zero ou NaN
            if pd.isna(ultimo_valor) or ultimo_valor == 0:
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados[coluna].iloc[-2]
                if hasattr(penultimo_valor, 'item'):
                    penultimo_valor = penultimo_valor.item()

                dados.loc[dados.index[-1], coluna] = penultimo_valor
                print(f"     🔧 Corrigido valor zero em {coluna}: {ultimo_valor} → {penultimo_valor}")

    return dados

def calcular_features_e_sinais(dados):
    """
    Calcula features e sinais de compra/venda baseados na média OHLC
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)
    
    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4
    
    # Calcular volatilidade (desvio padrão dos retornos em janela de 20 dias)
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=20).std() * 100
    
    # Calcular spread estimado usando volatilidade
    dados['Spread'] = dados['Volatilidade'] * 0.5  # Estimativa baseada na volatilidade
    
    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())
    
    # Criar sinais futuros (3 dias à frente)
    dados['Media_OHLC_Futura'] = dados['Media_OHLC'].shift(-3)
    
    # Sinal de venda: média OHLC atual > média OHLC 3 dias à frente
    dados['Sinal_Venda'] = (dados['Media_OHLC'] > dados['Media_OHLC_Futura']).astype(int)
    
    # Sinal de compra: média OHLC atual < média OHLC 3 dias à frente  
    dados['Sinal_Compra'] = (dados['Media_OHLC'] < dados['Media_OHLC_Futura']).astype(int)
    
    # Criar features de média OHLC passada (10 dias)
    for i in range(1, 11):
        dados[f'Media_OHLC_Lag_{i}'] = dados['Media_OHLC'].shift(i)
    
    # Remover linhas com NaN (início e fim da série)
    dados = dados.dropna()
    
    return dados

def preparar_dataset(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Features: média OHLC passada (10 dias), volume, spread, volatilidade
    feature_cols = [f'Media_OHLC_Lag_{i}' for i in range(1, 11)] + ['Volume', 'Spread', 'Volatilidade']

    # Verificar se todas as colunas existem
    colunas_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    if len(colunas_existentes) != len(feature_cols):
        print(f"⚠️ Algumas features não encontradas. Disponíveis: {len(colunas_existentes)}/{len(feature_cols)}")
        feature_cols = colunas_existentes

    X = dataset_completo[feature_cols]

    # Targets: sinais de compra e venda
    y_compra = dataset_completo['Sinal_Compra']
    y_venda = dataset_completo['Sinal_Venda']

    print(f"📊 Dataset preparado:")
    print(f"   • Total de amostras: {len(X)}")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Colunas de features: {feature_cols}")
    print(f"   • Sinais de compra (1): {y_compra.sum()} ({y_compra.mean()*100:.1f}%)")
    print(f"   • Sinais de venda (1): {y_venda.sum()} ({y_venda.mean()*100:.1f}%)")

    return X, y_compra, y_venda, feature_cols

def treinar_classificadores(X, y_compra, y_venda, feature_cols):
    """
    Treina classificadores XGBoost para sinais de compra e venda
    """
    # Normalizar features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_scaled = pd.DataFrame(X_scaled, columns=feature_cols)
    
    resultados = {}
    
    # Treinar classificador para sinais de compra
    print("\n🚀 Treinando classificador para sinais de COMPRA...")
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_compra, test_size=0.2, random_state=42, stratify=y_compra)
    
    modelo_compra = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        eval_metric='logloss'
    )
    
    modelo_compra.fit(X_train, y_train)
    y_pred_compra = modelo_compra.predict(X_test)
    
    print(f"   • Acurácia: {accuracy_score(y_test, y_pred_compra):.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred_compra, target_names=['Não Comprar', 'Comprar']))
    
    resultados['compra'] = {
        'modelo': modelo_compra,
        'scaler': scaler,
        'accuracy': accuracy_score(y_test, y_pred_compra),
        'y_test': y_test,
        'y_pred': y_pred_compra,
        'feature_importance': modelo_compra.feature_importances_
    }
    
    # Treinar classificador para sinais de venda
    print("\n🚀 Treinando classificador para sinais de VENDA...")
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_venda, test_size=0.2, random_state=42, stratify=y_venda)
    
    modelo_venda = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        eval_metric='logloss'
    )
    
    modelo_venda.fit(X_train, y_train)
    y_pred_venda = modelo_venda.predict(X_test)
    
    print(f"   • Acurácia: {accuracy_score(y_test, y_pred_venda):.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred_venda, target_names=['Não Vender', 'Vender']))
    
    resultados['venda'] = {
        'modelo': modelo_venda,
        'scaler': scaler,
        'accuracy': accuracy_score(y_test, y_pred_venda),
        'y_test': y_test,
        'y_pred': y_pred_venda,
        'feature_importance': modelo_venda.feature_importances_
    }
    
    return resultados, feature_cols

def criar_graficos_resultados(resultados, feature_cols):
    """
    Cria gráficos dos resultados dos classificadores
    """
    # Configurar estilo
    plt.style.use('default')
    
    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Resultados dos Classificadores XGBoost - Sinais de Trading', fontsize=16, fontweight='bold')
    
    # Matriz de confusão - Compra
    cm_compra = confusion_matrix(resultados['compra']['y_test'], resultados['compra']['y_pred'])
    sns.heatmap(cm_compra, annot=True, fmt='d', cmap='Blues', ax=axes[0,0])
    axes[0,0].set_title(f'Matriz de Confusão - Sinais de Compra\nAcurácia: {resultados["compra"]["accuracy"]:.3f}')
    axes[0,0].set_xlabel('Predito')
    axes[0,0].set_ylabel('Real')
    axes[0,0].set_xticklabels(['Não Comprar', 'Comprar'])
    axes[0,0].set_yticklabels(['Não Comprar', 'Comprar'])
    
    # Matriz de confusão - Venda
    cm_venda = confusion_matrix(resultados['venda']['y_test'], resultados['venda']['y_pred'])
    sns.heatmap(cm_venda, annot=True, fmt='d', cmap='Reds', ax=axes[0,1])
    axes[0,1].set_title(f'Matriz de Confusão - Sinais de Venda\nAcurácia: {resultados["venda"]["accuracy"]:.3f}')
    axes[0,1].set_xlabel('Predito')
    axes[0,1].set_ylabel('Real')
    axes[0,1].set_xticklabels(['Não Vender', 'Vender'])
    axes[0,1].set_yticklabels(['Não Vender', 'Vender'])
    
    # Feature importance - Compra
    importance_compra = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['compra']['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)
    
    axes[1,0].barh(importance_compra['feature'], importance_compra['importance'], color='skyblue')
    axes[1,0].set_title('Top 10 Features - Sinais de Compra')
    axes[1,0].set_xlabel('Importância')
    
    # Feature importance - Venda
    importance_venda = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['venda']['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)
    
    axes[1,1].barh(importance_venda['feature'], importance_venda['importance'], color='lightcoral')
    axes[1,1].set_title('Top 10 Features - Sinais de Venda')
    axes[1,1].set_xlabel('Importância')
    
    plt.tight_layout()
    
    # Salvar gráfico
    nome_arquivo = 'results/figures/classificador_xgboost_sinais.png'
    os.makedirs(os.path.dirname(nome_arquivo), exist_ok=True)
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Gráfico salvo: {nome_arquivo}")

def salvar_modelos(resultados, feature_cols):
    """
    Salva os modelos treinados e scaler para uso futuro
    """
    # Criar diretório para modelos
    modelo_dir = 'results/models'
    os.makedirs(modelo_dir, exist_ok=True)

    # Salvar modelo de compra
    modelo_compra_path = os.path.join(modelo_dir, 'xgboost_sinais_compra.pkl')
    with open(modelo_compra_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['compra']['modelo'],
            'scaler': resultados['compra']['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['compra']['accuracy']
        }, f)

    # Salvar modelo de venda
    modelo_venda_path = os.path.join(modelo_dir, 'xgboost_sinais_venda.pkl')
    with open(modelo_venda_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['venda']['modelo'],
            'scaler': resultados['venda']['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['venda']['accuracy']
        }, f)

    print(f"💾 Modelos salvos:")
    print(f"   • Modelo de Compra: {modelo_compra_path}")
    print(f"   • Modelo de Venda: {modelo_venda_path}")

    # Salvar resumo dos resultados
    resumo_path = os.path.join(modelo_dir, 'resumo_modelos.txt')
    with open(resumo_path, 'w') as f:
        f.write("RESUMO DOS MODELOS XGBOOST - SINAIS DE TRADING\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Data de treinamento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"Features utilizadas ({len(feature_cols)}):\n")
        for i, feature in enumerate(feature_cols, 1):
            f.write(f"  {i:2d}. {feature}\n")
        f.write(f"\nResultados:\n")
        f.write(f"  • Acurácia Sinais de Compra: {resultados['compra']['accuracy']:.3f}\n")
        f.write(f"  • Acurácia Sinais de Venda: {resultados['venda']['accuracy']:.3f}\n")
        f.write(f"\nDefinição dos sinais:\n")
        f.write(f"  • Sinal de Compra: Média OHLC atual < Média OHLC 3 dias à frente\n")
        f.write(f"  • Sinal de Venda: Média OHLC atual > Média OHLC 3 dias à frente\n")

    print(f"   • Resumo: {resumo_path}")

def salvar_sinais_csv(acoes_dados):
    """
    Salva todos os sinais de compra e venda em arquivo CSV
    """
    print(f"\n💾 Salvando sinais em CSV...")

    # Lista para armazenar todos os dados
    dados_completos = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Preparar dados para CSV
            dados_csv = dados.copy()
            dados_csv['Ticker'] = ticker.replace('.SA', '')  # Remover .SA para clareza

            # Selecionar colunas relevantes
            colunas_csv = [
                'Ticker', 'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
                'Sinal_Compra', 'Sinal_Venda', 'Media_OHLC_Futura'
            ]

            # Verificar se todas as colunas existem
            colunas_existentes = [col for col in colunas_csv if col in dados_csv.columns]

            if len(colunas_existentes) >= 6:  # Pelo menos as colunas principais
                dados_selecionados = dados_csv[colunas_existentes].copy()
                dados_selecionados.reset_index(inplace=True)
                dados_selecionados['Data'] = dados_selecionados['Date'].dt.strftime('%Y-%m-%d')
                dados_selecionados.drop('Date', axis=1, inplace=True)

                # Reordenar colunas
                cols_ordenadas = ['Ticker', 'Data'] + [col for col in colunas_existentes if col != 'Ticker']
                dados_selecionados = dados_selecionados[cols_ordenadas]

                dados_completos.append(dados_selecionados)

    if dados_completos:
        # Combinar todos os dados
        df_final = pd.concat(dados_completos, ignore_index=True)

        # Ordenar por ticker e data
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar CSV
        csv_path = 'results/sinais_xgboost_completo.csv'
        df_final.to_csv(csv_path, index=False)

        print(f"   ✅ Sinais salvos: {csv_path}")
        print(f"   📊 Total de registros: {len(df_final):,}")
        print(f"   📈 Ações processadas: {df_final['Ticker'].nunique()}")

        # Estatísticas dos sinais
        total_compra = df_final['Sinal_Compra'].sum()
        total_venda = df_final['Sinal_Venda'].sum()
        total_registros = len(df_final)

        print(f"   🟢 Sinais de Compra: {total_compra:,} ({total_compra/total_registros*100:.1f}%)")
        print(f"   🔴 Sinais de Venda: {total_venda:,} ({total_venda/total_registros*100:.1f}%)")

        # Salvar resumo por ação
        resumo_por_acao = df_final.groupby('Ticker').agg({
            'Sinal_Compra': 'sum',
            'Sinal_Venda': 'sum',
            'Media_OHLC': ['first', 'last'],
            'Data': 'count'
        }).round(2)

        resumo_por_acao.columns = ['Sinais_Compra', 'Sinais_Venda', 'Preco_Inicial', 'Preco_Final', 'Total_Dias']
        resumo_por_acao['Performance_%'] = ((resumo_por_acao['Preco_Final'] / resumo_por_acao['Preco_Inicial']) - 1) * 100

        resumo_path = 'results/resumo_sinais_por_acao.csv'
        resumo_por_acao.to_csv(resumo_path)
        print(f"   📋 Resumo por ação: {resumo_path}")

        return csv_path
    else:
        print(f"   ❌ Nenhum dado válido para salvar")
        return None

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🤖 CLASSIFICADOR XGBOOST - SINAIS DE TRADING")
    print("=" * 80)
    print("📊 Baseado na média OHLC das ações diversificadas")
    print("🎯 Sinais: Compra (preço atual < preço 3 dias à frente)")
    print("🎯 Sinais: Venda (preço atual > preço 3 dias à frente)")
    print("🔧 Features: Média OHLC passada (10 dias), Volume, Spread, Volatilidade")
    print("=" * 80)
    
    # Carregar lista de ações
    acoes = carregar_acoes_diversificadas()
    if not acoes:
        print("❌ Nenhuma ação encontrada")
        return
    
    # Baixar dados e calcular features
    print(f"\n📥 Baixando dados de {len(acoes)} ações...")
    acoes_dados = {}
    
    for ticker, nome in acoes:
        dados = baixar_dados_acao(ticker, nome)
        if dados is not None:
            dados_processados = calcular_features_e_sinais(dados)
            acoes_dados[ticker] = dados_processados
    
    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Salvar sinais em CSV
    salvar_sinais_csv(acoes_dados)

    # Preparar dataset
    print(f"\n🔧 Preparando dataset...")
    X, y_compra, y_venda, feature_cols = preparar_dataset(acoes_dados)
    
    if X is None:
        print("❌ Erro ao preparar dataset")
        return
    
    # Treinar classificadores
    resultados, feature_cols = treinar_classificadores(X, y_compra, y_venda, feature_cols)
    
    # Criar gráficos
    print(f"\n📊 Criando gráficos...")
    criar_graficos_resultados(resultados, feature_cols)

    # Salvar modelos
    print(f"\n💾 Salvando modelos...")
    salvar_modelos(resultados, feature_cols)

    print(f"\n✅ Análise concluída!")
    print(f"📊 Resultados:")
    print(f"   • Acurácia Sinais de Compra: {resultados['compra']['accuracy']:.3f}")
    print(f"   • Acurácia Sinais de Venda: {resultados['venda']['accuracy']:.3f}")

if __name__ == "__main__":
    main()
