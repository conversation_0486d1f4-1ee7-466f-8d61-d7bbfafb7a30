#!/usr/bin/env python3
"""
Exemplo de script adaptado para usar configuração centralizada
Script para análise das ações diversificadas usando médias móveis com config.yaml
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('acoes_diversificacao', 'acoes_diversificacao.csv')
        
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def carregar_acoes_carteira():
    """
    Carrega ações da carteira usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        carteira_file = file_paths.get('carteira', 'carteira.csv')
        
        carteira = pd.read_csv(carteira_file)
        
        acoes_carteira = []
        for _, row in carteira.iterrows():
            ticker = row['ticker']
            # Assumir que o nome é o ticker sem .SA
            nome = ticker.replace('.SA', '')
            acoes_carteira.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes_carteira)} ações da carteira: {carteira_file}")
        return acoes_carteira
        
    except Exception as e:
        print(f"❌ Erro ao carregar carteira: {e}")
        return []

def obter_dados_com_mm(ticker, nome):
    """
    Obtém dados e calcula médias móveis usando configurações
    """
    try:
        print(f"📊 Processando {ticker} - {nome}")
        
        # Usar configuração para período de dados
        data_config = config.get_data_config()
        period = data_config.get('periods', {}).get('default_period', '18mo')
        
        # Obter dados históricos
        stock = yf.Ticker(ticker)
        dados = stock.history(period=period)
        
        if dados.empty:
            print(f"     ❌ Nenhum dado encontrado para {ticker}")
            return None
            
        print(f"     📅 Dados obtidos: {len(dados)} dias")
        
        # Calcular média OHLC usando configuração
        use_ohlc = config.get('moving_averages.use_ohlc_average', True)
        if use_ohlc:
            dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4
        else:
            dados['Media_OHLC'] = dados['Close']
        
        # Calcular médias móveis usando configurações
        mm_windows = config.get_moving_average_windows()
        use_exponential = config.get('moving_averages.exponential_smoothing', True)
        
        if use_exponential:
            dados['MM10'] = dados['Media_OHLC'].ewm(span=mm_windows.get('mm10', 10)).mean()
            dados['MM25'] = dados['Media_OHLC'].ewm(span=mm_windows.get('mm25', 25)).mean()
            dados['MM100'] = dados['Media_OHLC'].ewm(span=mm_windows.get('mm100', 100)).mean()
            dados['MM200'] = dados['Media_OHLC'].ewm(span=mm_windows.get('mm200', 200)).mean()
        else:
            dados['MM10'] = dados['Media_OHLC'].rolling(window=mm_windows.get('mm10', 10)).mean()
            dados['MM25'] = dados['Media_OHLC'].rolling(window=mm_windows.get('mm25', 25)).mean()
            dados['MM100'] = dados['Media_OHLC'].rolling(window=mm_windows.get('mm100', 100)).mean()
            dados['MM200'] = dados['Media_OHLC'].rolling(window=mm_windows.get('mm200', 200)).mean()
        
        # Calcular spread usando configuração
        spread_config = config.get_spread_config()
        spread_window = spread_config.get('window', 20)
        
        try:
            spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=spread_window)
            if spread_config.get('convert_to_percentage', True):
                dados['Spread'] = spread_estimado * spread_config.get('percentage_multiplier', 100)
            else:
                dados['Spread'] = spread_estimado
        except Exception as e:
            print(f"     ⚠️ Erro ao calcular spread: {e}")
            # Fallback usando configuração
            if spread_config.get('use_volatility_fallback', True):
                returns = dados['Media_OHLC'].pct_change().dropna()
                volatility_window = spread_config.get('volatility_window', 20)
                volatilidade = returns.rolling(window=volatility_window).std()
                dados['Spread'] = volatilidade * 100
        
        # Calcular tendência
        dados['Tendencia'] = dados['Media_OHLC'].diff()
        
        return dados
        
    except Exception as e:
        print(f"     ❌ Erro ao processar {ticker}: {e}")
        return None

def criar_grafico_mm(ticker, nome, dados):
    """
    Cria gráfico com médias móveis usando configurações de visualização
    """
    # Obter configurações de visualização
    viz_config = config.get_visualization_config()
    colors = config.get_colors()
    line_widths = config.get_line_widths()
    figure_size = viz_config.get('figure_size', [16, 12])
    
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figure_size)
    
    # Calcular estatísticas
    preco_inicial = dados['Media_OHLC'].iloc[0]
    preco_final = dados['Media_OHLC'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    # Gráfico 1: Preços e Médias Móveis
    ax1.plot(dados.index, dados['Media_OHLC'], 
             linewidth=line_widths.get('price', 2.5), 
             color=colors.get('price', '#1f77b4'),
             label='Média OHLC', zorder=3)
    
    # Médias móveis usando configurações
    ax1.plot(dados.index, dados['MM10'], 
             linewidth=line_widths.get('mm10', 5), 
             color=colors.get('mm10', 'blue'),
             label='MM 10 dias', alpha=viz_config.get('alpha_values', {}).get('main_lines', 0.8), zorder=2)
    
    ax1.plot(dados.index, dados['MM25'], 
             linewidth=line_widths.get('mm25', 2), 
             color=colors.get('mm25', 'green'),
             label='MM 25 dias', alpha=viz_config.get('alpha_values', {}).get('main_lines', 0.8), zorder=2)
    
    ax1.plot(dados.index, dados['MM100'], 
             linewidth=line_widths.get('mm100', 2), 
             color=colors.get('mm100', 'purple'),
             label='MM 100 dias', alpha=viz_config.get('alpha_values', {}).get('main_lines', 0.8), zorder=2)
    
    ax1.plot(dados.index, dados['MM200'], 
             linewidth=line_widths.get('mm200', 2), 
             color=colors.get('mm200', 'red'),
             label='MM 200 dias', alpha=viz_config.get('alpha_values', {}).get('main_lines', 0.8), zorder=2)
    
    ax1.set_title(f'{ticker} - {nome} | Performance: {performance:.2f}%', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend(loc=viz_config.get('legend_position', 'upper left'))
    ax1.grid(True, alpha=0.3)
    
    # Gráfico 2: Volume
    ax2.bar(dados.index, dados['Volume'], 
            color=colors.get('volume', 'gray'), 
            alpha=viz_config.get('alpha_values', {}).get('volume_bars', 0.6))
    ax2.set_title('Volume de Negociação', fontsize=12)
    ax2.set_ylabel('Volume', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # Gráfico 3: Spread
    ax3.plot(dados.index, dados['Spread'], 
             color=colors.get('spread', 'brown'), 
             linewidth=2, label='Spread (%)')
    ax3.set_title('Spread Bid/Ask Estimado', fontsize=12)
    ax3.set_ylabel('Spread (%)', fontsize=10)
    ax3.set_xlabel('Data', fontsize=10)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Salvar usando configuração de diretórios
    output_dirs = config.get_output_directories()
    figures_dir = output_dirs.get('figures', 'results/figures')
    
    filename = f"{figures_dir}/mm_analysis_{ticker.replace('.SA', '')}.png"
    plt.savefig(filename, dpi=viz_config.get('dpi', 100), bbox_inches='tight')
    plt.close()
    
    print(f"     💾 Gráfico salvo: {filename}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_inicial': preco_inicial,
        'preco_final': preco_final
    }

def main():
    """
    Função principal usando configuração centralizada
    """
    print("📊 ANÁLISE COM MÉDIAS MÓVEIS - USANDO CONFIGURAÇÃO CENTRALIZADA")
    print("="*80)
    
    # Configurar ambiente automaticamente
    setup_environment()
    
    # Mostrar configurações sendo usadas
    mm_windows = config.get_moving_average_windows()
    print("📊 Configurações carregadas:")
    print(f"   • Janelas MM: {mm_windows}")
    print(f"   • Usar média OHLC: {config.get('moving_averages.use_ohlc_average')}")
    print(f"   • Suavização exponencial: {config.get('moving_averages.exponential_smoothing')}")
    print(f"   • Período de dados: {config.get('data.periods.default_period')}")
    
    # Carregar ações
    acoes_diversificadas = carregar_acoes_diversificadas()
    acoes_carteira = carregar_acoes_carteira()
    
    # Combinar todas as ações
    todas_acoes = list(set(acoes_diversificadas + acoes_carteira))
    
    if not todas_acoes:
        print("❌ Nenhuma ação foi carregada")
        return
    
    print(f"\n🚀 Processando {len(todas_acoes)} ações...")
    
    resultados = []
    sucesso = 0
    erro = 0
    
    for i, (ticker, nome) in enumerate(todas_acoes, 1):
        print(f"\n[{i:2d}/{len(todas_acoes)}]", end=" ")
        
        dados = obter_dados_com_mm(ticker, nome)
        
        if dados is not None:
            resultado = criar_grafico_mm(ticker, nome, dados)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1
    
    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")
    
    if resultados:
        # Salvar resultados usando configuração
        output_dirs = config.get_output_directories()
        csv_dir = output_dirs.get('csv', 'results/csv')
        
        df_resultados = pd.DataFrame(resultados)
        csv_path = f"{csv_dir}/analise_mm_com_config.csv"
        df_resultados.to_csv(csv_path, index=False)
        print(f"   💾 Resultados salvos: {csv_path}")
        
        # Mostrar top 5 performances
        top_5 = df_resultados.nlargest(5, 'performance')
        print(f"\n🏆 TOP 5 PERFORMANCES:")
        for _, row in top_5.iterrows():
            print(f"   {row['ticker']:<10} {row['nome']:<20} {row['performance']:>8.2f}%")

if __name__ == "__main__":
    main()
