#!/usr/bin/env python3
"""
Script de teste integrado para executar todas as análises:
- <PERSON><PERSON><PERSON><PERSON> (MM)
- Filtro Butterworth
- Classificador XGBoost
"""

import os
import sys
import subprocess
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

def executar_script(script_path, nome_analise):
    """
    Executa um script Python e monitora o resultado
    """
    print(f"\n{'='*60}")
    print(f"🚀 EXECUTANDO: {nome_analise}")
    print(f"📄 Script: {script_path}")
    print(f"⏰ Início: {datetime.now().strftime('%H:%M:%S')}")
    print(f"{'='*60}")
    
    inicio = time.time()
    
    try:
        # Executar o script
        resultado = subprocess.run([
            'uv', 'run', 'python', script_path
        ], capture_output=True, text=True, cwd='/home/<USER>/finance')
        
        fim = time.time()
        duracao = fim - inicio
        
        if resultado.returncode == 0:
            print(f"✅ {nome_analise} - CONCLUÍDO")
            print(f"⏱️ Duração: {duracao:.1f} segundos")
            
            # Mostrar últimas linhas da saída
            linhas_saida = resultado.stdout.strip().split('\n')
            if len(linhas_saida) > 5:
                print(f"📋 Últimas linhas da saída:")
                for linha in linhas_saida[-5:]:
                    if linha.strip():
                        print(f"   {linha}")
            
            return True
        else:
            print(f"❌ {nome_analise} - ERRO")
            print(f"⏱️ Duração: {duracao:.1f} segundos")
            print(f"🔴 Código de erro: {resultado.returncode}")
            
            if resultado.stderr:
                print(f"📋 Erro:")
                print(resultado.stderr)
            
            return False
            
    except Exception as e:
        fim = time.time()
        duracao = fim - inicio
        print(f"❌ {nome_analise} - EXCEÇÃO")
        print(f"⏱️ Duração: {duracao:.1f} segundos")
        print(f"🔴 Erro: {e}")
        return False

def verificar_arquivos_gerados():
    """
    Verifica se os arquivos esperados foram gerados
    """
    print(f"\n{'='*60}")
    print(f"📊 VERIFICANDO ARQUIVOS GERADOS")
    print(f"{'='*60}")
    
    arquivos_esperados = {
        'MM': [
            'results/figures/mm_analysis',  # Diretório com gráficos individuais
            'results/csv/mm_data'  # Diretório com dados CSV
        ],
        'Butterworth': [
            'results/figures/butterworth_analysis',  # Diretório com gráficos individuais
            'results/csv/butterworth_data'  # Diretório com dados CSV
        ],
        'XGBoost': [
            'results/figures/classificador_xgboost_sinais.png',
            'results/sinais_xgboost_completo.csv',
            'results/models/xgboost_sinais_compra.pkl',
            'results/models/xgboost_sinais_venda.pkl'
        ]
    }
    
    total_arquivos = 0
    arquivos_encontrados = 0
    
    for analise, arquivos in arquivos_esperados.items():
        print(f"\n📈 {analise}:")
        for arquivo in arquivos:
            total_arquivos += 1
            if os.path.exists(arquivo):
                if os.path.isdir(arquivo):
                    # Contar arquivos no diretório
                    num_arquivos = len([f for f in os.listdir(arquivo) if os.path.isfile(os.path.join(arquivo, f))])
                    print(f"   ✅ {arquivo}/ ({num_arquivos} arquivos)")
                else:
                    tamanho = os.path.getsize(arquivo)
                    print(f"   ✅ {arquivo} ({tamanho:,} bytes)")
                arquivos_encontrados += 1
            else:
                print(f"   ❌ {arquivo} - NÃO ENCONTRADO")
    
    print(f"\n📊 Resumo: {arquivos_encontrados}/{total_arquivos} arquivos encontrados")
    return arquivos_encontrados, total_arquivos

def mostrar_configuracoes():
    """
    Mostra as configurações que serão usadas
    """
    print(f"\n{'='*60}")
    print(f"⚙️ CONFIGURAÇÕES UTILIZADAS")
    print(f"{'='*60}")
    
    # Configurações gerais
    print(f"📅 Período padrão: {config.get('data.periods.default_period')}")
    print(f"📁 Arquivo ações diversificadas: {config.get('files.acoes_diversificacao')}")
    
    # Configurações MM
    print(f"\n📈 Médias Móveis:")
    mm_periods = config.get('moving_averages.periods')
    print(f"   Períodos: {mm_periods}")
    
    # Configurações Butterworth
    print(f"\n🔄 Filtro Butterworth:")
    print(f"   Ordem: {config.get('butterworth.order')}")
    print(f"   Frequência de corte: {config.get('butterworth.cutoff_frequency')}")
    
    # Configurações XGBoost
    print(f"\n🤖 XGBoost:")
    print(f"   Período de dados: {config.get('xgboost.data_period')}")
    print(f"   Horizonte de sinais: {config.get('xgboost.signal_horizon')} dias")
    print(f"   Lags OHLC: {config.get('xgboost.features.ohlc_lags')}")
    print(f"   N estimators: {config.get('xgboost.model_params.n_estimators')}")

def main():
    """
    Função principal - executa todas as análises
    """
    # Configurar ambiente
    setup_environment()
    
    print("🔬 TESTE INTEGRADO - ANÁLISES FINANCEIRAS")
    print("=" * 60)
    print("📊 Este script executa todas as análises em sequência:")
    print("   1. Análise de Médias Móveis")
    print("   2. Análise com Filtro Butterworth") 
    print("   3. Classificador XGBoost para Sinais de Trading")
    print("=" * 60)
    
    # Mostrar configurações
    mostrar_configuracoes()
    
    # Lista de scripts para executar
    scripts = [
        ('src/analise_mm_acoes_diversificadas.py', 'Análise Médias Móveis'),
        ('src/analise_butterworth_acoes_diversificadas.py', 'Análise Filtro Butterworth'),
        ('src/classificador_xgboost_sinais.py', 'Classificador XGBoost')
    ]
    
    # Executar cada script
    inicio_total = time.time()
    resultados = []
    
    for script_path, nome_analise in scripts:
        if os.path.exists(script_path):
            sucesso = executar_script(script_path, nome_analise)
            resultados.append((nome_analise, sucesso))
        else:
            print(f"❌ Script não encontrado: {script_path}")
            resultados.append((nome_analise, False))
    
    fim_total = time.time()
    duracao_total = fim_total - inicio_total
    
    # Verificar arquivos gerados
    arquivos_encontrados, total_arquivos = verificar_arquivos_gerados()
    
    # Resumo final
    print(f"\n{'='*60}")
    print(f"📋 RESUMO FINAL")
    print(f"{'='*60}")
    print(f"⏱️ Tempo total: {duracao_total:.1f} segundos")
    print(f"📊 Arquivos gerados: {arquivos_encontrados}/{total_arquivos}")
    
    sucessos = sum(1 for _, sucesso in resultados if sucesso)
    print(f"✅ Análises bem-sucedidas: {sucessos}/{len(resultados)}")
    
    print(f"\n📈 Resultados por análise:")
    for nome, sucesso in resultados:
        status = "✅ SUCESSO" if sucesso else "❌ ERRO"
        print(f"   {nome}: {status}")
    
    if sucessos == len(resultados) and arquivos_encontrados == total_arquivos:
        print(f"\n🎉 TESTE INTEGRADO CONCLUÍDO COM SUCESSO!")
        print(f"📊 Todos os scripts executaram corretamente")
        print(f"📁 Todos os arquivos foram gerados")
        print(f"\n📂 Verifique os resultados em:")
        print(f"   • results/figures/ - Gráficos")
        print(f"   • results/csv/ - Dados CSV")
        print(f"   • results/models/ - Modelos XGBoost")
    else:
        print(f"\n⚠️ TESTE INTEGRADO CONCLUÍDO COM PROBLEMAS")
        print(f"❌ Alguns scripts falharam ou arquivos não foram gerados")
        print(f"🔍 Verifique os logs acima para detalhes")
    
    return sucessos == len(resultados)

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
