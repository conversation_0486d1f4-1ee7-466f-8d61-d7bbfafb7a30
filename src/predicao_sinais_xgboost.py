#!/usr/bin/env python3
"""
Script para fazer predições de sinais de compra e venda usando modelos XGBoost treinados
Carrega modelos salvos e faz predições para ações específicas
"""

import yfinance as yf
import pandas as pd
import numpy as np
import os
import sys
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

def carregar_modelos():
    """
    Carrega os modelos XGBoost treinados
    """
    modelo_dir = 'results/models'
    
    # Carregar modelo de compra
    modelo_compra_path = os.path.join(modelo_dir, 'xgboost_sinais_compra.pkl')
    if not os.path.exists(modelo_compra_path):
        print(f"❌ Modelo de compra não encontrado: {modelo_compra_path}")
        return None, None
    
    with open(modelo_compra_path, 'rb') as f:
        dados_compra = pickle.load(f)
    
    # Carregar modelo de venda
    modelo_venda_path = os.path.join(modelo_dir, 'xgboost_sinais_venda.pkl')
    if not os.path.exists(modelo_venda_path):
        print(f"❌ Modelo de venda não encontrado: {modelo_venda_path}")
        return None, None
    
    with open(modelo_venda_path, 'rb') as f:
        dados_venda = pickle.load(f)
    
    print(f"✅ Modelos carregados:")
    print(f"   • Modelo de Compra - Acurácia: {dados_compra['accuracy']:.3f}")
    print(f"   • Modelo de Venda - Acurácia: {dados_venda['accuracy']:.3f}")
    print(f"   • Features: {len(dados_compra['feature_cols'])}")
    
    return dados_compra, dados_venda

def baixar_dados_acao(ticker):
    """
    Baixa dados históricos recentes de uma ação
    """
    try:
        print(f"📊 Baixando dados de {ticker}...")
        
        # Baixar dados dos últimos 3 meses para ter dados suficientes
        dados = yf.download(ticker, period='3mo', progress=False)
        
        if dados.empty:
            print(f"❌ Nenhum dado encontrado para {ticker}")
            return None
        
        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)
            
        return dados
        
    except Exception as e:
        print(f"❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def preparar_features(dados):
    """
    Prepara features para predição seguindo o mesmo padrão do treinamento
    """
    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4
    
    # Calcular volatilidade (desvio padrão dos retornos em janela de 20 dias)
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=20).std() * 100
    
    # Calcular spread estimado usando volatilidade
    dados['Spread'] = dados['Volatilidade'] * 0.5
    
    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())
    
    # Criar features de média OHLC passada (10 dias)
    for i in range(1, 11):
        dados[f'Media_OHLC_Lag_{i}'] = dados['Media_OHLC'].shift(i)
    
    # Remover linhas com NaN
    dados = dados.dropna()
    
    return dados

def fazer_predicao(ticker, dados_compra, dados_venda):
    """
    Faz predição de sinais para uma ação específica
    """
    # Baixar dados
    dados = baixar_dados_acao(ticker)
    if dados is None:
        return None
    
    # Preparar features
    dados_processados = preparar_features(dados)
    if len(dados_processados) == 0:
        print(f"❌ Dados insuficientes para {ticker}")
        return None
    
    # Extrair features do último dia
    feature_cols = dados_compra['feature_cols']
    ultimo_dia = dados_processados.iloc[-1]
    
    # Verificar se todas as features estão disponíveis
    features_disponiveis = [col for col in feature_cols if col in dados_processados.columns]
    if len(features_disponiveis) != len(feature_cols):
        print(f"⚠️ Algumas features não disponíveis para {ticker}")
        return None
    
    X = ultimo_dia[feature_cols].values.reshape(1, -1)
    
    # Normalizar usando o scaler treinado
    X_scaled = dados_compra['scaler'].transform(X)
    
    # Fazer predições
    prob_compra = dados_compra['modelo'].predict_proba(X_scaled)[0]
    prob_venda = dados_venda['modelo'].predict_proba(X_scaled)[0]
    
    pred_compra = dados_compra['modelo'].predict(X_scaled)[0]
    pred_venda = dados_venda['modelo'].predict(X_scaled)[0]
    
    # Obter dados atuais
    preco_atual = ultimo_dia['Media_OHLC']
    volume_atual = ultimo_dia['Volume']
    volatilidade_atual = ultimo_dia['Volatilidade']
    
    resultado = {
        'ticker': ticker,
        'data': dados_processados.index[-1].strftime('%Y-%m-%d'),
        'preco_atual': preco_atual,
        'volume': volume_atual,
        'volatilidade': volatilidade_atual,
        'sinal_compra': pred_compra,
        'prob_compra': prob_compra[1],  # Probabilidade de compra
        'sinal_venda': pred_venda,
        'prob_venda': prob_venda[1],    # Probabilidade de venda
        'confianca_compra': max(prob_compra),
        'confianca_venda': max(prob_venda)
    }
    
    return resultado

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🔮 PREDIÇÃO DE SINAIS - XGBOOST")
    print("=" * 50)
    
    # Carregar modelos
    dados_compra, dados_venda = carregar_modelos()
    if dados_compra is None or dados_venda is None:
        print("❌ Erro ao carregar modelos. Execute primeiro o script de treinamento.")
        return
    
    # Lista de ações para predição (algumas das ações diversificadas)
    acoes_teste = [
        'TIMS3.SA', 'WEGE3.SA', 'STBP3.SA', 'SYNE3.SA', 'LVTC3.SA'
    ]
    
    print(f"\n🎯 Fazendo predições para {len(acoes_teste)} ações...")
    print("=" * 50)
    
    resultados = []
    
    for ticker in acoes_teste:
        try:
            resultado = fazer_predicao(ticker, dados_compra, dados_venda)
            if resultado:
                resultados.append(resultado)
                
                # Mostrar resultado
                print(f"\n📊 {ticker} ({resultado['data']}):")
                print(f"   💰 Preço: R$ {resultado['preco_atual']:.2f}")
                print(f"   📈 Volume: {resultado['volume']:,.0f}")
                print(f"   📊 Volatilidade: {resultado['volatilidade']:.2f}%")
                
                # Sinais
                if resultado['sinal_compra'] == 1:
                    print(f"   🟢 SINAL DE COMPRA (Prob: {resultado['prob_compra']:.1%}, Conf: {resultado['confianca_compra']:.1%})")
                else:
                    print(f"   ⚪ Sem sinal de compra (Prob: {resultado['prob_compra']:.1%})")
                
                if resultado['sinal_venda'] == 1:
                    print(f"   🔴 SINAL DE VENDA (Prob: {resultado['prob_venda']:.1%}, Conf: {resultado['confianca_venda']:.1%})")
                else:
                    print(f"   ⚪ Sem sinal de venda (Prob: {resultado['prob_venda']:.1%})")
                    
        except Exception as e:
            print(f"❌ Erro ao processar {ticker}: {e}")
    
    # Resumo
    if resultados:
        print(f"\n📋 RESUMO DAS PREDIÇÕES")
        print("=" * 50)
        
        sinais_compra = [r for r in resultados if r['sinal_compra'] == 1]
        sinais_venda = [r for r in resultados if r['sinal_venda'] == 1]
        
        print(f"🟢 Sinais de COMPRA: {len(sinais_compra)}")
        for r in sinais_compra:
            print(f"   • {r['ticker']} - Prob: {r['prob_compra']:.1%}")
        
        print(f"\n🔴 Sinais de VENDA: {len(sinais_venda)}")
        for r in sinais_venda:
            print(f"   • {r['ticker']} - Prob: {r['prob_venda']:.1%}")
        
        # Salvar resultados
        df_resultados = pd.DataFrame(resultados)
        resultado_path = 'results/predicoes_sinais_xgboost.csv'
        df_resultados.to_csv(resultado_path, index=False)
        print(f"\n💾 Resultados salvos em: {resultado_path}")
    
    print(f"\n✅ Predições concluídas!")

if __name__ == "__main__":
    main()
