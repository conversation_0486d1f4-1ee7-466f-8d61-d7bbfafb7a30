#!/usr/bin/env python3
"""
Teste das médias móveis reversas
"""

import sys
sys.path.append('src')
import yfinance as yf
import pandas as pd
import numpy as np

def calcular_mm_reversa_adaptavel(dados, serie, janela_desejada):
    """
    Calcula média móvel reversa com janela adaptável
    Usa janelas menores quando há menos amostras disponíveis
    """
    serie_limpa = serie.dropna()
    if len(serie_limpa) == 0:
        return pd.Series(index=dados.index, dtype=float)
    
    # Determinar janela efetiva baseada na quantidade de dados disponíveis
    janela_efetiva = min(janela_desejada, len(serie_limpa))
    
    # Aplicar média móvel reversa
    # Inverter a série, aplicar rolling mean, e inverter novamente
    serie_invertida = serie_limpa[::-1]
    mm_reversa_invertida = serie_invertida.rolling(window=janela_efetiva, min_periods=1).mean()
    mm_reversa = mm_reversa_invertida[::-1]
    
    # Reindexar para manter o índice original
    mm_reversa.index = serie_limpa.index
    
    # Criar série completa com NaN onde necessário
    resultado = pd.Series(index=dados.index, dtype=float)
    resultado.loc[mm_reversa.index] = mm_reversa.values
    
    return resultado

def test_mm_reversa():
    """Testa as médias móveis reversas"""
    
    # Testar com uma ação
    ticker = 'PETR4.SA'
    print(f"Testando médias móveis reversas com {ticker}...")
    
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period='6mo')
        
        if dados.empty:
            print("❌ Não foi possível obter dados")
            return False
            
        print(f"✅ Dados obtidos: {len(dados)} dias")
        
        # Calcular média OHLC
        dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4
        
        # Calcular MM200 tradicional
        dados['MM200'] = dados['Media_OHLC'].rolling(window=200).mean()
        
        # Calcular médias móveis reversas
        dados['MM25_Rev'] = calcular_mm_reversa_adaptavel(dados, dados['Media_OHLC'], 25)
        dados['MM200_Rev'] = calcular_mm_reversa_adaptavel(dados, dados['Media_OHLC'], 200)
        
        # Mostrar últimos valores
        print("\n📊 Últimos 5 valores:")
        print("Data\t\tMedia_OHLC\tMM200\t\tMM25_Rev\tMM200_Rev")
        print("-" * 70)

        for i in range(-5, 0):
            data = dados.index[i].strftime('%Y-%m-%d')
            media_ohlc = dados['Media_OHLC'].iloc[i]
            mm200 = dados['MM200'].iloc[i] if not pd.isna(dados['MM200'].iloc[i]) else 0
            mm25_rev = dados['MM25_Rev'].iloc[i] if not pd.isna(dados['MM25_Rev'].iloc[i]) else 0
            mm200_rev = dados['MM200_Rev'].iloc[i] if not pd.isna(dados['MM200_Rev'].iloc[i]) else 0

            print(f"{data}\t{media_ohlc:.2f}\t\t{mm200:.2f}\t\t{mm25_rev:.2f}\t\t{mm200_rev:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    test_mm_reversa()
