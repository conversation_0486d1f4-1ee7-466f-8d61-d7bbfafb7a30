#!/usr/bin/env python3
import pandas as pd
import sys
sys.path.append('src')
from analise_carteira import carregar_carteira, obter_dados_historicos, calcular_rendimento_individual, calcular_rendimento_carteira

def debug_valores():
    carteira = carregar_carteira('carteira.csv')
    tickers = carteira['ticker'].unique()
    from datetime import datetime, timedelta
    data_inicio = datetime.now() - timedelta(days=365)
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    resultados = calcular_rendimento_individual(carteira, dados_historicos)
    carteira_total = calcular_rendimento_carteira(resultados)
    
    print("=== VALORES DO PRIMEIRO GRÁFICO ===")
    historico = carteira_total['historico']
    print("02/07 - Valor_Total (linha azul):", historico.loc['2025-07-02', 'Valor_Total'])
    print("02/07 - Valor_Investido_Acumulado (linha vermelha):", historico.loc['2025-07-02', 'Valor_Investido_Acumulado'])
    
    print("\n=== VERIFICAÇÃO PETR3 ===")
    for resultado in resultados:
        if resultado['ticker'] == 'PETR3.SA':
            hist = resultado['dados_historicos']
            print("PETR3 em 02/07:")
            linha_02_07 = hist.loc['2025-07-02']
            print(f"  Quantidade_Acumulada: {linha_02_07['Quantidade_Acumulada']}")
            print(f"  Close: {linha_02_07['Close']:.2f}")
            print(f"  Valor_Posicao: {linha_02_07['Valor_Posicao']:.2f}")
            print(f"  Valor_Investido_Acumulado: {linha_02_07['Valor_Investido_Acumulado']:.2f}")
            break

if __name__ == "__main__":
    debug_valores()
