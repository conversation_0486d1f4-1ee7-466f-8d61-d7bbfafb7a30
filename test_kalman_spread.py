#!/usr/bin/env python3
"""
Teste simples para verificar se o filtro de Kalman com spread está funcionando sem NaN
"""

import sys
import os
sys.path.append('src')

import numpy as np
import pandas as pd
from kalman import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_kalman_spread():
    """
    Teste básico do filtro de Kalman com spread
    """
    print("🧪 Testando Filtro de Kalman com Spread...")
    
    # Dados de teste simples
    precos = pd.Series([100, 101, 102, 101, 103, 104, 103, 105, 106, 107])
    spread = pd.Series([0.1, 0.12, 0.11, 0.13, 0.1, 0.09, 0.11, 0.1, 0.12, 0.11])
    
    print(f"Preços: {precos.tolist()}")
    print(f"Spread: {spread.tolist()}")
    
    # Importar função de configuração
    from analise_kalman_acoes_diversificadas import configurar_filtro_kalman_com_spread, aplicar_filtro_kalman_com_spread
    
    try:
        # Testar configuração
        kf, spread_normalizado = configurar_filtro_kalman_com_spread(precos, spread)
        print("✅ Configuração do filtro OK")
        print(f"Spread normalizado: {spread_normalizado.tolist()}")
        
        # Testar aplicação
        resultado = aplicar_filtro_kalman_com_spread(precos, spread)
        
        if resultado[0] is not None:
            precos_filtrados, kf, estados, covariancias, spread_norm = resultado
            print("✅ Aplicação do filtro OK")
            print(f"Preços filtrados: {precos_filtrados}")
            
            # Verificar se há NaN
            if np.isnan(precos_filtrados).any():
                print("❌ ERRO: Preços filtrados contêm NaN!")
                return False
            else:
                print("✅ Nenhum NaN encontrado nos preços filtrados")
                return True
        else:
            print("❌ ERRO: Falha na aplicação do filtro")
            return False
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        return False

if __name__ == "__main__":
    success = test_kalman_spread()
    if success:
        print("\n🎉 Teste passou! O filtro está funcionando corretamente.")
    else:
        print("\n💥 Teste falhou! Há problemas com o filtro.")
