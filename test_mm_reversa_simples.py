#!/usr/bin/env python3
"""
Teste simples das médias móveis reversas
"""

import pandas as pd
import numpy as np

def test_mm_reversa_simples():
    """Testa com dados sintéticos simples"""
    
    # Criar dados sintéticos simples
    dados = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 
                     index=pd.date_range('2024-01-01', periods=10))
    
    print("📊 Dados originais:")
    print(dados)
    
    # Média móvel tradicional de 3 períodos
    mm_tradicional = dados.rolling(window=3).mean()
    print("\n📊 MM tradicional (3 períodos):")
    print(mm_tradicional)
    
    # Média móvel reversa de 3 períodos
    # Inverter, aplicar rolling, inverter novamente
    dados_invertidos = dados[::-1]
    print("\n📊 Dados invertidos:")
    print(dados_invertidos)
    
    mm_reversa_invertida = dados_invertidos.rolling(window=3).mean()
    print("\n📊 MM reversa invertida:")
    print(mm_reversa_invertida)
    
    mm_reversa = mm_reversa_invertida[::-1]
    print("\n📊 MM reversa final:")
    print(mm_reversa)
    
    # Reindexar para manter ordem original
    mm_reversa.index = dados.index
    print("\n📊 MM reversa com índice original:")
    print(mm_reversa)

if __name__ == "__main__":
    test_mm_reversa_simples()
