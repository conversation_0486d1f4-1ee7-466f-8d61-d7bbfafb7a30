#!/usr/bin/env python3
"""
Teste final das médias móveis reversas
"""

import sys
sys.path.append('src')
import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

def calcular_medias_moveis(dados):
    """
    Calcula médias móveis de 25 e 200 dias baseadas na média OHLC
    Inclui médias móveis reversas aplicadas sobre as médias móveis tradicionais
    """
    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

    # Calcular médias móveis tradicionais (forward-looking)
    dados['MM25'] = dados['Media_OHLC'].rolling(window=25).mean()
    dados['MM200'] = dados['Media_OHLC'].rolling(window=200).mean()

    # Calcular médias móveis reversas SOBRE as médias móveis tradicionais com janelas adaptáveis
    # MM25 Reversa sobre MM25 tradicional
    mm25_limpa = dados['MM25'].dropna()
    if len(mm25_limpa) > 0:
        janela_25 = min(25, len(mm25_limpa))
        mm25_invertida = mm25_limpa[::-1]
        mm25_rev_sobre_mm25 = mm25_invertida.rolling(window=janela_25, min_periods=1).mean()[::-1]
        dados['MM25_Rev'] = np.nan
        dados.loc[mm25_rev_sobre_mm25.index, 'MM25_Rev'] = mm25_rev_sobre_mm25.values
    else:
        dados['MM25_Rev'] = np.nan



    # MM200 Reversa sobre MM200 tradicional
    mm200_limpa = dados['MM200'].dropna()
    if len(mm200_limpa) > 0:
        janela_200 = min(200, len(mm200_limpa))
        mm200_invertida = mm200_limpa[::-1]
        mm200_rev_sobre_mm200 = mm200_invertida.rolling(window=janela_200, min_periods=1).mean()[::-1]
        dados['MM200_Rev'] = np.nan
        dados.loc[mm200_rev_sobre_mm200.index, 'MM200_Rev'] = mm200_rev_sobre_mm200.values
    else:
        dados['MM200_Rev'] = np.nan

    return dados

def test_mm_final():
    """Testa as médias móveis reversas com uma ação"""
    
    # Testar com uma ação
    ticker = 'PETR4.SA'
    print(f"Testando médias móveis reversas com {ticker}...")
    
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period='15mo')
        
        if dados.empty:
            print("❌ Não foi possível obter dados")
            return False
            
        print(f"✅ Dados obtidos: {len(dados)} dias")
        
        # Calcular médias móveis
        dados = calcular_medias_moveis(dados)
        
        # Pegar últimos 12 meses
        dados_12m = dados.tail(252)
        
        # Mostrar últimos valores
        print("\n📊 Últimos 5 valores:")
        print("Data\t\tMedia_OHLC\tMM200\t\tMM25_Rev")
        print("-" * 60)
        
        for i in range(-5, 0):
            data = dados_12m.index[i].strftime('%Y-%m-%d')
            media_ohlc = dados_12m['Media_OHLC'].iloc[i]
            mm200 = dados_12m['MM200'].iloc[i] if not pd.isna(dados_12m['MM200'].iloc[i]) else 0
            mm25_rev = dados_12m['MM25_Rev'].iloc[i] if not pd.isna(dados_12m['MM25_Rev'].iloc[i]) else 0

            print(f"{data}\t{media_ohlc:.2f}\t\t{mm200:.2f}\t\t{mm25_rev:.2f}")
        
        # Criar gráfico simples
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        
        ax.plot(dados_12m.index, dados_12m['Media_OHLC'], linewidth=2.5, color='#1f77b4', label='Média OHLC')
        ax.plot(dados_12m.index, dados_12m['MM200'], linewidth=2, color='red', label='MM 200 dias (tradicional)', alpha=0.8)
        
        if not dados_12m['MM25_Rev'].isna().all():
            ax.plot(dados_12m.index, dados_12m['MM25_Rev'], linewidth=2, color='darkgreen', linestyle='--', label='MM25 Reversa', alpha=0.7)
        
        ax.set_title(f'{ticker} - Médias Móveis Reversas + MM200 Tradicional', fontsize=14, fontweight='bold')
        ax.set_ylabel('Valor (R$)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('test_mm_final.png', dpi=300, bbox_inches='tight')
        print(f"\n✅ Gráfico salvo como test_mm_final.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    test_mm_final()
