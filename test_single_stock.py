#!/usr/bin/env python3
"""
Teste com uma única ação para verificar se o script principal está funcionando
"""

import sys
import os
sys.path.append('src')

def test_single_stock():
    """
    Testa o processamento de uma única ação
    """
    print("🧪 Testando processamento de uma única ação...")
    
    from analise_kalman_acoes_diversificadas import obter_dados_com_kalman_spread, criar_grafico_kalman
    
    # Testar com PETR4 (Petrobras)
    ticker = "PETR4.SA"
    nome = "Petrobras"
    
    print(f"Testando: {ticker} - {nome}")
    
    try:
        resultado = obter_dados_com_kalman_spread(ticker, nome)
        
        if resultado is not None:
            if len(resultado) == 4:
                dados, previsoes, kf, data_corte = resultado
                print("✅ Dados obtidos com sucesso")
                print(f"   Dados: {len(dados)} registros")
                print(f"   Previsões: {len(previsoes) if previsoes is not None else 0} dias")
                
                # Verificar se há NaN nos dados principais
                if dados['Kalman'].isna().any():
                    print("❌ ERRO: Dad<PERSON>lman contêm NaN")
                    return False
                else:
                    print("✅ Dados Kalman sem NaN")
                
                # Criar gráfico
                resultado_grafico = criar_grafico_kalman(ticker, nome, dados, previsoes, data_corte)
                print("✅ Gráfico criado com sucesso")
                print(f"   Performance: {resultado_grafico['performance']:.2f}%")
                print(f"   Tendência: {resultado_grafico['tendencia']}")
                
                return True
            else:
                print("❌ ERRO: Formato de resultado inesperado")
                return False
        else:
            print("❌ ERRO: Falha ao obter dados")
            return False
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_single_stock()
    if success:
        print("\n🎉 Teste passou! O script principal está funcionando corretamente.")
    else:
        print("\n💥 Teste falhou! Há problemas no script principal.")
