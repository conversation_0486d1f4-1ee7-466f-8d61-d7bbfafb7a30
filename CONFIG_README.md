# 🔧 Sistema de Configuração Centralizada

Este projeto agora possui um sistema de configuração centralizada que permite gerenciar todos os parâmetros dos scripts em um único arquivo YAML.

## 📁 Arquivos do Sistema

- **`config.yaml`** - Arquivo principal de configuração com todos os parâmetros
- **`src/config_loader.py`** - Módulo para carregar e gerenciar as configurações
- **`src/exemplo_uso_config.py`** - Exemplos de como usar o sistema
- **`src/analise_mm_com_config.py`** - Exemplo de script adaptado

## 🚀 Como Usar

### 1. Executar Exemplo Demonstrativo

```bash
uv run python src/exemplo_uso_config.py
```

Este comando mostra:
- Como carregar configurações
- Exemplos de adaptação de scripts
- Configurações disponíveis
- Templates de código

### 2. Testar Script Adaptado

```bash
uv run python src/analise_mm_com_config.py
```

Este script demonstra um exemplo real de como adaptar um script existente para usar o sistema de configuração.

## 📝 Como Adaptar Seus Scripts

### Passo 1: Importar o Sistema

```python
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import config, setup_environment
```

### Passo 2: Configurar Ambiente

```python
def main():
    # Configura matplotlib, warnings, TensorFlow e cria diretórios automaticamente
    setup_environment()
    
    # Resto do código...
```

### Passo 3: Substituir Valores Hardcoded

**Antes:**
```python
# Valores hardcoded
mm25_window = 25
mm200_window = 200
color_mm25 = 'green'
epochs = 50
carteira_file = 'carteira.csv'
```

**Depois:**
```python
# Usando configuração
mm_windows = config.get_moving_average_windows()
colors = config.get_colors()
lstm_config = config.get_lstm_config()
file_paths = config.get_file_paths()

mm25_window = mm_windows.get('mm25', 25)
mm200_window = mm_windows.get('mm200', 200)
color_mm25 = colors.get('mm25', 'green')
epochs = lstm_config.get('training', {}).get('epochs', 50)
carteira_file = file_paths.get('carteira', 'carteira.csv')
```

## 🎛️ Principais Configurações Disponíveis

### Médias Móveis
```python
mm_windows = config.get_moving_average_windows()
# Retorna: {'mm10': 10, 'mm25': 25, 'mm50': 50, 'mm100': 100, 'mm200': 200}
```

### LSTM
```python
lstm_config = config.get_lstm_config()
# Acesso: lstm_config['training']['epochs'], lstm_config['architecture']['lstm_units'], etc.
```

### Kalman
```python
kalman_config = config.get_kalman_config()
# Acesso: kalman_config['process_noise_covariance'], kalman_config['prediction_days'], etc.
```

### Cores e Visualização
```python
colors = config.get_colors()
line_widths = config.get_line_widths()
viz_config = config.get_visualization_config()
```

### Arquivos e Diretórios
```python
file_paths = config.get_file_paths()
output_dirs = config.get_output_directories()
```

## 🔧 Modificar Parâmetros

Para alterar qualquer parâmetro, edite o arquivo `config.yaml`:

```yaml
# Exemplo: Alterar janelas das médias móveis
moving_averages:
  windows:
    mm10: 15    # Era 10
    mm25: 30    # Era 25
    mm200: 250  # Era 200

# Exemplo: Alterar cores
visualization:
  colors:
    mm25: 'blue'    # Era 'green'
    mm200: 'purple' # Era 'red'

# Exemplo: Alterar configurações LSTM
lstm:
  training:
    epochs: 100     # Era 50
    batch_size: 64  # Era 32
```

**Todos os scripts adaptados usarão automaticamente os novos valores!**

## 📊 Funções de Conveniência

O módulo `config_loader.py` oferece funções de conveniência para acesso rápido:

```python
from config_loader import (
    get_config,                    # Acesso geral: get_config('data.periods.default_period')
    get_moving_average_windows,    # Janelas das médias móveis
    get_colors,                    # Cores para gráficos
    get_file_paths,                # Caminhos dos arquivos
    setup_environment              # Configurar ambiente completo
)
```

## 🗂️ Estrutura do config.yaml

O arquivo está organizado em seções:

- **`general`** - Configurações gerais (matplotlib, warnings, etc.)
- **`data`** - Arquivos, períodos, datas, correções
- **`moving_averages`** - Janelas e configurações das médias móveis
- **`butterworth`** - Configurações do filtro Butterworth
- **`kalman`** - Configurações do filtro de Kalman
- **`lstm`** - Configurações do modelo LSTM
- **`correlation`** - Configurações de análise de correlação
- **`visualization`** - Cores, tamanhos, estilos de gráficos
- **`trading_strategy`** - Configurações de estratégia de trading
- **`output`** - Diretórios e formatos de saída
- **`api`** - Chaves e configurações de API
- **`spread`** - Configurações de cálculo de spread
- **`performance`** - Configurações de performance

## ✅ Vantagens do Sistema

1. **Centralização** - Todos os parâmetros em um local
2. **Consistência** - Mesmos valores em todos os scripts
3. **Facilidade** - Alterar um valor afeta todos os scripts
4. **Organização** - Parâmetros organizados por categoria
5. **Flexibilidade** - Valores padrão se configuração não existir
6. **Automação** - Configuração automática do ambiente

## 🔄 Migração Gradual

Você pode migrar seus scripts gradualmente:

1. **Mantenha** os scripts originais funcionando
2. **Adapte** um script por vez
3. **Teste** cada adaptação
4. **Compare** resultados entre versões
5. **Substitua** quando estiver satisfeito

## 🆘 Solução de Problemas

### Erro: "Arquivo de configuração não encontrado"
- Verifique se `config.yaml` está na raiz do projeto
- Verifique o caminho no import

### Erro: "Configuração não carregada"
- Verifique a sintaxe do YAML
- Use um validador YAML online se necessário

### Valores não aplicados
- Verifique se está usando `config.get()` corretamente
- Verifique se a chave existe no YAML
- Use valores padrão como fallback

## 📚 Próximos Passos

1. Execute os exemplos para entender o sistema
2. Adapte um script simples primeiro
3. Teste e compare resultados
4. Adapte gradualmente outros scripts
5. Personalize as configurações conforme necessário

---

**💡 Dica:** Use o arquivo `src/exemplo_uso_config.py` como referência e o `src/analise_mm_com_config.py` como template para suas adaptações!
