# Classificador XGBoost para Sinais de Trading

## Descrição

Este projeto implementa um classificador XGBoost para identificar sinais de compra e venda em ações brasileiras baseado na análise da média OHLC (Open, High, Low, Close) e outras features técnicas.

## Arquivos Principais

### 1. `src/classificador_xgboost_sinais.py`
Script principal que treina os modelos XGBoost para classificação de sinais de trading.

**Funcionalidades:**
- Baixa dados históricos das ações diversificadas
- Calcula features técnicas (média OHLC, volatilidade, spread)
- Cria sinais de compra e venda baseados em previsão de 3 dias
- Treina classificadores XGBoost separados para compra e venda
- Gera gráficos de análise (matrizes de confusão, feature importance)
- Salva modelos treinados para uso futuro

### 2. `src/predicao_sinais_xgboost.py`
Script para fazer predições usando os modelos treinados.

**Funcionalidades:**
- Carrega modelos XGBoost salvos
- Faz predições em tempo real para ações específicas
- Calcula probabilidades de sinais de compra e venda
- Salva resultados em CSV

## Metodologia

### Definição dos Sinais

**Sinal de Compra (1):** Média OHLC atual < Média OHLC 3 dias à frente
- Indica que o preço atual está abaixo do preço esperado em 3 dias
- Sugere oportunidade de compra

**Sinal de Venda (1):** Média OHLC atual > Média OHLC 3 dias à frente
- Indica que o preço atual está acima do preço esperado em 3 dias
- Sugere oportunidade de venda

### Features Utilizadas (13 total)

1. **Média OHLC Passada (10 features):**
   - `Media_OHLC_Lag_1` até `Media_OHLC_Lag_10`
   - Valores históricos da média OHLC dos últimos 10 dias

2. **Volume:** Volume de negociação do dia

3. **Spread:** Estimativa do spread bid/ask baseado na volatilidade

4. **Volatilidade:** Desvio padrão dos retornos em janela de 20 dias

### Processamento dos Dados

1. **Cálculo da Média OHLC:** `(Open + High + Low + Close) / 4`
2. **Normalização:** StandardScaler para padronizar as features
3. **Divisão:** 80% treino, 20% teste
4. **Validação:** Estratificada para manter proporção das classes

## Resultados

### Métricas de Performance
- **Acurácia Sinais de Compra:** ~58.5%
- **Acurácia Sinais de Venda:** ~58.6%

### Interpretação
- Modelos apresentam performance moderada, típica para problemas de trading
- Balanceamento entre precisão e recall
- Feature importance mostra relevância das médias OHLC passadas

## Como Usar

### 1. Treinar os Modelos
```bash
uv run python src/classificador_xgboost_sinais.py
```

**Saídas:**
- `results/figures/classificador_xgboost_sinais.png` - Gráficos de análise
- `results/models/xgboost_sinais_compra.pkl` - Modelo de compra
- `results/models/xgboost_sinais_venda.pkl` - Modelo de venda
- `results/models/resumo_modelos.txt` - Resumo dos resultados

### 2. Fazer Predições
```bash
uv run python src/predicao_sinais_xgboost.py
```

**Saídas:**
- `results/predicoes_sinais_xgboost.csv` - Predições em CSV
- Console com sinais em tempo real

## Dependências

- `xgboost` - Algoritmo de machine learning
- `scikit-learn` - Métricas e pré-processamento
- `yfinance` - Download de dados financeiros
- `pandas`, `numpy` - Manipulação de dados
- `matplotlib`, `seaborn` - Visualização

## Configuração

O script usa o arquivo `config.yaml` para:
- Período de dados históricos (18 meses)
- Arquivo de ações diversificadas
- Configurações de visualização

## Limitações

1. **Dados de Conectividade:** Dependente da disponibilidade do Yahoo Finance
2. **Horizonte de Predição:** Limitado a 3 dias à frente
3. **Features Técnicas:** Baseado apenas em preço, volume e volatilidade
4. **Mercado Brasileiro:** Focado em ações da B3

## Melhorias Futuras

1. **Features Adicionais:**
   - Indicadores técnicos (RSI, MACD, Bollinger Bands)
   - Dados fundamentalistas
   - Sentimento de mercado

2. **Modelos Avançados:**
   - Ensemble methods
   - Deep learning (LSTM, Transformer)
   - Modelos específicos para séries temporais

3. **Validação:**
   - Walk-forward analysis
   - Backtesting com custos de transação
   - Análise de risco

## Exemplo de Uso

```python
# Carregar modelos
dados_compra, dados_venda = carregar_modelos()

# Fazer predição para uma ação
resultado = fazer_predicao('WEGE3.SA', dados_compra, dados_venda)

# Verificar sinais
if resultado['sinal_compra'] == 1:
    print(f"Sinal de COMPRA para {resultado['ticker']}")
    print(f"Probabilidade: {resultado['prob_compra']:.1%}")

if resultado['sinal_venda'] == 1:
    print(f"Sinal de VENDA para {resultado['ticker']}")
    print(f"Probabilidade: {resultado['prob_venda']:.1%}")
```

## Aviso Legal

Este sistema é apenas para fins educacionais e de pesquisa. Não constitui aconselhamento financeiro. Sempre consulte um profissional qualificado antes de tomar decisões de investimento.
